import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const prepareBaseQuery = (baseUrl) =>
  fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers) => {
      const token = sessionStorage.getItem('token');
      if (token) headers.set('Authorization', `Bearer ${token}`);
      return headers;
    }
  });

export const createCustomApi = (reducerPath, baseUrl, tagTypes) =>
  createApi({
    reducerPath,
    baseQuery: prepareBaseQuery(baseUrl),
    tagTypes,
    // eslint-disable-next-line no-unused-vars
    endpoints: (builder) => ({})
  });

export const authApi = createCustomApi('loginApi', import.meta.env.VITE_BASE_URL, ['Login']);
export const menuApi = createCustomApi('menuApi', import.meta.env.VITE_BASE_URL, ['Menu']);
export const centerCounselorPanelApi = createCustomApi(
  'centerCounselor',
  import.meta.env.VITE_BASE_URL,
  ['CenterInfo']
);

export const faceRegisterApi = createCustomApi('faceRegisterApi', import.meta.env.VITE_BASE_URL, [
  'FaceRegister'
]);
export const processSelectorApi = createCustomApi(
  'processSelectorApi',
  import.meta.env.VITE_BASE_URL,
  ['ProcessSelector']
);
export const createOwnTestApi = createCustomApi('createOwnTestApi', import.meta.env.VITE_BASE_URL, [
  'CreateOwnTest'
]);
export const createOwnTestBioApi = createCustomApi('BioApi', import.meta.env.VITE_BASE_URL, [
  'CreateOwnTestBio'
]);
export const createOwnTestMathApi = createCustomApi('MathApi', import.meta.env.VITE_BASE_URL, [
  'CreateOwnTestMath'
]);
export const createOwnTestPhysicsApi = createCustomApi(
  'PhysicsApi',
  import.meta.env.VITE_BASE_URL,
  ['CreateOwnTestPhysics']
);
export const createOwnTestChemistryApi = createCustomApi(
  'ChemistryApi',
  import.meta.env.VITE_BASE_URL,
  ['CreateOwnTestChemistry']
);

export const chatApi = createCustomApi('chatApi', import.meta.env.VITE_BASE_URL, ['Chat']);

export const teacherDashboardApi = createCustomApi(
  'teacherDashboardApi',
  import.meta.env.VITE_BASE_URL,
  ['TeacherDashboard']
);
export const eventDetailsApi = createCustomApi('eventDetailsApi', import.meta.env.VITE_BASE_URL, [
  'EventDetails'
]);
export const liveStreamingApi = createCustomApi('liveStreamingApi', import.meta.env.VITE_BASE_URL, [
  'LiveStreaming'
]);

export const CenterTraineeDashboardApi = createCustomApi(
  'CenterTraineeDashboardApi',
  import.meta.env.VITE_BASE_URL,
  ['CenterTraineeDashboardApi']
);

export const CenterliveViewerApi = createCustomApi(
  'CenterliveViewerApi',
  import.meta.env.VITE_BASE_URL,
  ['CenterliveViewerApi']
);

// export const StudentInfoApi = createCustomApi(
//  'StudentInfoApi',
//    import.meta.env.VITE_BASE_URL,
//    ['StudentInfo']
// );

export const parentApi = createCustomApi('parentApi', import.meta.env.VITE_BASE_URL, [
  'studentInfo'
]);

export const onBroadingAssessmentApi = createCustomApi(
  'onBroadingAssessmentApi',
  import.meta.env.VITE_BASE_URL,
  ['OnBroadingAssessment']
);

export const CreatePhysicsTestApi = createCustomApi(
  'CreatePhysicsTestApi',
  import.meta.env.VITE_BASE_URL,
  ['CreatePhysicsTest']
);

export const PaperBasedBioTestApi = createCustomApi(
  'PaperBasedBioTestApi',
  import.meta.env.VITE_BASE_URL,
  ['PaperBasedBioTest']
);
export const PaperBasedPhysicsTestApi = createCustomApi(
  'PaperBasedPhysicsTestApi',
  import.meta.env.VITE_BASE_URL,
  ['PaperBasedPhysicsTest']
);
export const PaperBasedChemistryTestApi = createCustomApi(
  'PaperBasedChemistryTestApi',
  import.meta.env.VITE_BASE_URL,
  ['PaperBasedChemistryTest']
);
export const PaperBasedMathsTestApi = createCustomApi(
  'PaperBasedMathsTestApi',
  import.meta.env.VITE_BASE_URL,
  ['PaperBasedMathsTest']
);

export const DashboardUserDetailsApi = createCustomApi(
  'DashboardUserDetailsApi',
  import.meta.env.VITE_BASE_URL,
  ['DashboardUserDetails']
);
export const VirtualLabsApi = createCustomApi('VirtualLabsApi', import.meta.env.VITE_LABS_URL, [
  'VirtualLabs'
]);
export const UpcomingEventsApi = createCustomApi(
  'UpcomingEventsApi',
  import.meta.env.VITE_BASE_URL,
  ['UpcomingEvents']
);
export const directorApi = createCustomApi('director', import.meta.env.VITE_BASE_URL, [
  ['AddCenter']
]);

export const studentDashboardApi = createCustomApi(
  'studentDashboardApi',
  import.meta.env.VITE_BASE_URL,
  ['StudentDashboard']
);
export const parentStudentScoresApi = createCustomApi(
  'parentStudentScoresApi',
  import.meta.env.VITE_BASE_URL,
  ['parentStudentScoresApi']
);
export const recommendationApi = createCustomApi(
  'recommendationApi',
  import.meta.env.VITE_BASE_URL,
  ['Recommendation']
);

export const AnalyticsApi = createCustomApi(
  'AnalyticsApi',
  import.meta.env.VITE_BASE_URL,
  ['Analytics']
);