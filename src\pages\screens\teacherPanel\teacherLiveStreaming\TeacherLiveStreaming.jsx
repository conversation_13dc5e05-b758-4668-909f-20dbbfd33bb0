﻿
// import { useState, useEffect, useRef } from "react"
// import { Room, RoomEvent, createLocalVideoTrack, createLocalAudioTrack, createLocalScreenTracks } from "livekit-client"
// import { useDispatch, useSelector } from "react-redux"
// import { motion, AnimatePresence } from "framer-motion"
// import {
//   useStartEnhancedStreamMutation,
//   useStopEnhancedStreamMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
//   useUploadCbtPaperMutation,
//   setStreamingData,
//   setIsStreaming,
//   setError,
//   setChatMessages,
//   setChatLoading,
//   setChatError,
//   clearStreamingData,
// } from "./teacherLiveStreaming.slice"

// const TeacherLiveStreaming = () => {
//   const dispatch = useDispatch()
//   const { isStreaming, streamToken, roomName, error, chatMessages, chatLoading, chatError } = useSelector(
//     (state) => state.liveStreaming || {},
//   )

//   const [startStream] = useStartEnhancedStreamMutation()
//   const [stopStream] = useStopEnhancedStreamMutation()
//   const [sendChatMessageMutation] = useSendChatMessageMutation()
//   const [getChatHistory] = useLazyGetChatHistoryQuery()
//   const [uploadCbtPaper] = useUploadCbtPaperMutation()

//   // LiveKit states
//   const [livekitRoom, setLivekitRoom] = useState(null)
//   const [livekitConnected, setLivekitConnected] = useState(false)
//   const [livekitToken, setLivekitToken] = useState(null)
//   const [livekitUrl, setLivekitUrl] = useState(null)
//   const [sessionId, setSessionId] = useState(null)

//   // Media states
//   const [localVideoTrack, setLocalVideoTrack] = useState(null)
//   const [localAudioTrack, setLocalAudioTrack] = useState(null)
//   const [localScreenTrack, setLocalScreenTrack] = useState(null)
//   const [localScreenAudioTrack, setLocalScreenAudioTrack] = useState(null)
//   const [isScreenSharing, setIsScreenSharing] = useState(false)
//   const [previewVideoTrack, setPreviewVideoTrack] = useState(null)
//   const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false)
//   const [screenStream, setScreenStream] = useState(null)

//   // UI states
//   const [streamStatus, setStreamStatus] = useState("Ready to start streaming")
//   const [participants, setParticipants] = useState([])
//   const [quality, setQuality] = useState("medium")
//   const [activeSidebarTab, setActiveSidebarTab] = useState("status") // New state for sidebar tabs: 'status', 'viewers'

//   // Chat states
//   const [newMessage, setNewMessage] = useState("")
//   const [isChatOpen, setIsChatOpen] = useState(false)
//   const [unreadMessages, setUnreadMessages] = useState(0)
//   const [joinedViewers, setJoinedViewers] = useState([])

//   // Socket.IO states
//   const [socket, setSocket] = useState(null)
//   const [socketConnected, setSocketConnected] = useState(false)

//   // Quiz states
//   const [showQuizUpload, setShowQuizUpload] = useState(false)
//   const [quizFile, setQuizFile] = useState(null)
//   const [isUploadingQuiz, setIsUploadingQuiz] = useState(false)

//   // Translation states
//   const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
//   const [translationSession, setTranslationSession] = useState(null)
//   const [translationWebSocket, setTranslationWebSocket] = useState(null)
//   const [speechRecognition, setSpeechRecognition] = useState(null)
//   const [currentTranscription, setCurrentTranscription] = useState("")
//   const [sourceLanguage, setSourceLanguage] = useState("en")
//   const [translationStatus, setTranslationStatus] = useState("")
//   const [microphoneActive, setMicrophoneActive] = useState(false)

//   // Refs
//   const videoRef = useRef(null)
//   const screenVideoRef = useRef(null)
//   const pipCameraRef = useRef(null)
//   const livekitRoomRef = useRef(null)
//   const translationWebSocketRef = useRef(null)
//   const sharedMicrophoneStreamRef = useRef(null)

//   // Available languages for translation
//   const availableSourceLanguages = [
//     { code: "en", name: "English", flag: "🇺🇸" },
//     { code: "ta", name: "Tamil", flag: "🇮🇳" },
//     { code: "hi", name: "Hindi", flag: "🇮🇳" },
//     { code: "te", name: "Telugu", flag: "🇮🇳" },
//     { code: "kn", name: "Kannada", flag: "🇮🇳" },
//   ]

//   // Check secure context
//   useEffect(() => {
//     if (!window.isSecureContext) {
//       console.error("❌ App not running in secure context. Camera access requires HTTPS.")
//       setStreamStatus("Error: Camera access requires HTTPS.")
//       dispatch(setError("Camera access requires a secure context (HTTPS)."))
//     }
//   }, [])

//   // Initialize session ID on mount
//   useEffect(() => {
//     const userId = sessionStorage.getItem("userId")
//     const newSessionId = `teacher_${userId}_${Date.now()}`
//     setSessionId(newSessionId)
//   }, [])

//   // Initialize camera preview when streaming starts
//   useEffect(() => {
//     if (isStreaming) {
//       initializeCameraPreview()
//     }
//     return () => {
//       if (!isStreaming && previewVideoTrack) {
//         previewVideoTrack.stop()
//         setPreviewVideoTrack(null)
//       }
//     }
//   }, [isStreaming])

//   // Attach video track when previewVideoTrack changes
//   useEffect(() => {
//     if (previewVideoTrack && videoRef.current && !isScreenSharing) {
//       previewVideoTrack.attach(videoRef.current)
//       console.log("✅ Preview video track attached via useEffect")
//     }
//   }, [previewVideoTrack, isScreenSharing])

//   // Connect to LiveKit room when token and URL are available
//   useEffect(() => {
//     if (livekitToken && livekitUrl && !livekitRoom) {
//       connectToLiveKitRoom()
//     }
//   }, [livekitToken, livekitUrl])

//   // Publish camera track when localVideoTrack is available and room is connected
//   useEffect(() => {
//     const publishCameraTrack = async () => {
//       if (localVideoTrack && livekitRoom && livekitConnected) {
//         try {
//           const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values())
//           const existingPublication = publishedTracks.find(
//             (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
//           )
//           if (!existingPublication) {
//             await livekitRoom.localParticipant.publishTrack(localVideoTrack, {
//               source: "camera",
//               name: "teacher_camera",
//             })
//             console.log("✅ Camera track published to LiveKit via useEffect with camera source")
//           } else {
//             console.log("ℹ️ Camera track already published")
//           }
//         } catch (err) {
//           console.error("❌ Failed to publish camera track via useEffect:", err)
//         }
//       }
//     }
//     publishCameraTrack()
//   }, [localVideoTrack, livekitRoom, livekitConnected])

//   // Handle screen stream changes
//   useEffect(() => {
//     if (screenStream && screenVideoRef.current && isScreenSharing) {
//       console.log("🖥️ Setting screen stream to video element")
//       screenVideoRef.current.srcObject = screenStream
//       screenVideoRef.current.play().catch((err) => {
//         console.warn("⚠️ Screen video autoplay failed:", err)
//       })
//     }
//   }, [screenStream, isScreenSharing])

//   // Handle PiP camera attachment during screen sharing
//   useEffect(() => {
//     if (isScreenSharing && previewVideoTrack && pipCameraRef.current) {
//       try {
//         if (pipCameraRef.current.srcObject) {
//           pipCameraRef.current.srcObject = null
//         }
//         previewVideoTrack.attach(pipCameraRef.current)
//         console.log("✅ Camera attached to PiP via useEffect")
//         pipCameraRef.current.play().catch((playErr) => {
//           console.warn("⚠️ PiP camera autoplay failed:", playErr)
//         })
//       } catch (attachErr) {
//         console.warn("⚠️ Failed to attach camera to PiP via useEffect:", attachErr)
//         try {
//           const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack])
//           pipCameraRef.current.srcObject = mediaStream
//           console.log("✅ Camera attached to PiP via MediaStream fallback in useEffect")
//         } catch (fallbackErr) {
//           console.error("❌ PiP camera fallback failed in useEffect:", fallbackErr)
//         }
//       }
//     }
//   }, [isScreenSharing, previewVideoTrack])

//   // Periodic check to ensure parallel streaming remains active
//   useEffect(() => {
//     let intervalId
//     if (isStreaming && livekitConnected) {
//       intervalId = setInterval(() => {
//         if (isScreenSharing) {
//           ensureParallelStreaming()
//         }
//       }, 10000)
//       console.log("🔄 Started periodic parallel streaming check")
//     }
//     return () => {
//       if (intervalId) {
//         clearInterval(intervalId)
//         console.log("🛑 Stopped periodic parallel streaming check")
//       }
//     }
//   }, [isStreaming, livekitConnected, isScreenSharing])

//   // HTTP-based chat system
//   useEffect(() => {
//     if (isStreaming && sessionId) {
//       console.log("💬 TEACHER: Starting HTTP-based chat for session:", sessionId)
//       setSocketConnected(true)
//       setStreamStatus("Chat system ready")
//       loadChatHistory()
//       const pollInterval = setInterval(() => {
//         loadChatHistory()
//       }, 2000)
//       return () => {
//         clearInterval(pollInterval)
//         setSocketConnected(false)
//       }
//     }
//   }, [isStreaming, sessionId])

//   // Cleanup translation when component unmounts
//   useEffect(() => {
//     return () => {
//       cleanupTranslationResources()
//     }
//   }, [])

//   // Load chat history via Redux
//   const loadChatHistory = async () => {
//     if (!sessionId) return
//     try {
//       dispatch(setChatLoading(true))
//       dispatch(setChatError(null))
//       const result = await getChatHistory(sessionId)
//       if (result.data) {
//         const newMessages = result.data
//         const currentMessageCount = chatMessages.length
//         if (newMessages.length > currentMessageCount) {
//           const newCount = newMessages.length - currentMessageCount
//           if (newCount > 0 && !isChatOpen) {
//             setUnreadMessages((prev) => prev + newCount)
//           }
//         }
//         dispatch(setChatMessages(newMessages))
//       } else if (result.error) {
//         dispatch(setChatError(result.error.data || "Failed to load chat history"))
//       }
//     } catch (error) {
//       console.log("❌ TEACHER: Failed to load chat history:", error.message)
//       dispatch(setChatError(error.message || "Failed to load chat history"))
//     } finally {
//       dispatch(setChatLoading(false))
//     }
//   }

//   const connectToLiveKitRoom = async () => {
//     try {
//       console.log("🔗 Connecting to LiveKit room...")
//       const room = new Room()
//       room.on(RoomEvent.Connected, () => {
//         console.log("✅ Connected to LiveKit room")
//         setLivekitConnected(true)
//         setStreamStatus("Connected to LiveKit room")
//         setTimeout(() => {
//           const publishedTracks = room.localParticipant.videoTracks
//           if (publishedTracks && publishedTracks.values) {
//             console.log(
//               "📹 Published video tracks:",
//               Array.from(publishedTracks.values()).map((pub) => ({
//                 source: pub.source,
//                 name: pub.trackName,
//                 sid: pub.trackSid,
//               })),
//             )
//           } else {
//             console.log("📹 Published video tracks: none available yet")
//           }
//         }, 1000)
//       })

//       room.on(RoomEvent.Disconnected, () => {
//         console.log("❌ Disconnected from LiveKit room")
//         setLivekitConnected(false)
//         setStreamStatus("Disconnected from LiveKit room")
//       })

//       room.on(RoomEvent.ParticipantConnected, (participant) => {
//         console.log("👤 LiveKit Participant connected:", participant.identity, participant.name)
//         setParticipants((prev) => [...prev, participant])
//         setJoinedViewers((prev) => {
//           const existing = prev.find((v) => v.viewer_id === participant.identity)
//           if (!existing) {
//             return [
//               ...prev,
//               {
//                 viewer_id: participant.identity,
//                 viewer_name: participant.name || participant.identity,
//                 user_role: "student",
//                 joined_at: new Date().toISOString(),
//                 source: "livekit",
//               },
//             ]
//           }
//           return prev
//         })
//       })

//       room.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         console.log("👤 LiveKit Participant disconnected:", participant.identity)
//         setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
//         setJoinedViewers((prev) => prev.filter((v) => v.viewer_id !== participant.identity))
//       })

//       if (!livekitUrl) {
//         throw new Error("LiveKit URL is not set")
//       }

//       await room.connect(livekitUrl, livekitToken)
//       setLivekitRoom(room)
//       livekitRoomRef.current = room

//       const cameraTrack = localVideoTrack || previewVideoTrack
//       if (cameraTrack) {
//         try {
//           await room.localParticipant.publishTrack(cameraTrack, {
//             source: "camera",
//             name: "teacher_camera",
//           })
//           console.log("✅ Camera track published to LiveKit on connect with camera source")
//           if (!localVideoTrack && previewVideoTrack) {
//             setLocalVideoTrack(previewVideoTrack)
//           }
//         } catch (cameraErr) {
//           console.error("❌ Failed to publish camera track on connect:", cameraErr)
//         }
//       } else {
//         console.log("⚠️ No camera track available to publish on connect")
//       }

//       try {
//         // Use shared microphone stream for LiveKit audio
//         if (sharedMicrophoneStreamRef.current) {
//           console.log("🎤 Using shared microphone stream for LiveKit...")

//           // Get the audio track from shared stream
//           const sharedAudioTrack = sharedMicrophoneStreamRef.current.getAudioTracks()[0]
//           console.log("🎤 Shared audio track:", sharedAudioTrack)

//           if (sharedAudioTrack) {
//             // Create LiveKit audio track from shared microphone track
//             const audioTrack = await createLocalAudioTrack({
//               deviceId: sharedAudioTrack.getSettings().deviceId,
//               echoCancellation: true,
//               noiseSuppression: true,
//               autoGainControl: true
//             })

//             setLocalAudioTrack(audioTrack)
//             await room.localParticipant.publishTrack(audioTrack, {
//               name: "teacher_microphone",
//               source: "microphone"
//             })

//             console.log("✅ Audio track published to LiveKit using shared microphone device")

//             // Verify audio is working
//             setTimeout(() => {
//               const publishedTracks = room.localParticipant.audioTracks
//               console.log("📊 Published audio tracks:", Array.from(publishedTracks.values()).map(pub => ({
//                 name: pub.trackName,
//                 enabled: pub.track?.enabled,
//                 muted: pub.track?.isMuted
//               })))
//             }, 1000)
//           } else {
//             throw new Error("No audio track found in shared microphone stream")
//           }
//         } else {
//           console.log("⚠️ No shared microphone stream, creating new audio track...")
//           // Fallback to creating new audio track
//           const audioTrack = await createLocalAudioTrack()
//           setLocalAudioTrack(audioTrack)
//           await room.localParticipant.publishTrack(audioTrack)
//           console.log("✅ Audio track published to LiveKit (fallback)")
//         }
//       } catch (audioErr) {
//         console.error("❌ Could not create audio track:", audioErr)

//         // Try alternative approach - publish raw audio track
//         try {
//           if (sharedMicrophoneStreamRef.current) {
//             console.log("🔄 Trying alternative approach - publishing raw audio track...")
//             const sharedAudioTrack = sharedMicrophoneStreamRef.current.getAudioTracks()[0]
//             if (sharedAudioTrack) {
//               await room.localParticipant.publishTrack(sharedAudioTrack, {
//                 name: "teacher_microphone_raw",
//                 source: "microphone"
//               })
//               console.log("✅ Raw audio track published to LiveKit")
//             }
//           }
//         } catch (fallbackErr) {
//           console.error("❌ Fallback audio publishing also failed:", fallbackErr)
//         }
//       }
//     } catch (err) {
//       console.error("❌ Failed to connect to LiveKit room:", err)
//       setStreamStatus("Failed to connect to LiveKit room")
//       dispatch(setError(err.message || "Failed to connect to LiveKit room"))
//     }
//   }

//   const initializeSharedMicrophone = async () => {
//     try {
//       console.log("🎤 Initializing shared microphone stream...")

//       // Create shared microphone stream
//       const microphoneStream = await navigator.mediaDevices.getUserMedia({
//         audio: {
//           echoCancellation: true,
//           noiseSuppression: true,
//           autoGainControl: true,
//           sampleRate: 44100
//         }
//       })

//       sharedMicrophoneStreamRef.current = microphoneStream
//       console.log("✅ Shared microphone stream created")

//       // Add audio level monitoring for debugging
//       const audioContext = new (window.AudioContext || window.webkitAudioContext)()
//       const source = audioContext.createMediaStreamSource(microphoneStream)
//       const analyser = audioContext.createAnalyser()
//       source.connect(analyser)

//       const dataArray = new Uint8Array(analyser.frequencyBinCount)
//       const checkAudioLevel = () => {
//         analyser.getByteFrequencyData(dataArray)
//         const average = dataArray.reduce((a, b) => a + b) / dataArray.length
//         if (average > 10) { // Audio detected
//           console.log("🎤 Audio level detected:", average)
//           setMicrophoneActive(true)
//           // Reset microphone active state after 1 second of no audio
//           setTimeout(() => setMicrophoneActive(false), 1000)
//         }
//       }

//       // Check audio levels every 2 seconds for debugging
//       const audioLevelInterval = setInterval(checkAudioLevel, 2000)

//       // Store interval for cleanup
//       microphoneStream.audioLevelInterval = audioLevelInterval
//       microphoneStream.audioContext = audioContext

//       return microphoneStream
//     } catch (err) {
//       console.error("❌ Failed to initialize shared microphone:", err)
//       throw err
//     }
//   }

//   const initializeCameraPreview = async () => {
//     try {
//       console.log("🎥 Initializing camera preview...")

//       // Initialize shared microphone first
//       await initializeSharedMicrophone()

//       const videoTrack = await createLocalVideoTrack({
//         resolution: { width: 640, height: 480 },
//         frameRate: 15,
//       })
//       console.log("✅ Video track created:", videoTrack)
//       setPreviewVideoTrack(videoTrack)
//       setCameraPermissionGranted(true)
//       if (videoRef.current) {
//         videoTrack.attach(videoRef.current)
//         console.log("✅ Preview video track attached")
//       } else {
//         console.log("⚠️ videoRef not ready yet, will attach via useEffect")
//       }
//     } catch (err) {
//       console.error("❌ Error initializing camera preview:", err)
//       setCameraPermissionGranted(false)
//       let errorMessage = "Failed to initialize camera preview"
//       if (err.name === "NotAllowedError") {
//         errorMessage = "Camera permission denied. Please allow camera access."
//       } else if (err.name === "NotFoundError") {
//         errorMessage = "No camera found. Please connect a camera."
//       } else if (err.name === "NotReadableError") {
//         errorMessage = "Camera in use by another application."
//       } else if (err.message) {
//         errorMessage = err.message
//       }
//       setStreamStatus(errorMessage)
//       dispatch(setError(errorMessage))
//     }
//   }

//   const refreshCameraTrack = async () => {
//     try {
//       console.log("🔄 Refreshing camera track...")
//       if (previewVideoTrack) {
//         previewVideoTrack.stop()
//         previewVideoTrack.detach()
//       }
//       const newVideoTrack = await createLocalVideoTrack({
//         resolution: { width: 640, height: 480 },
//         frameRate: 15,
//       })
//       console.log("✅ New camera track created:", newVideoTrack)
//       setPreviewVideoTrack(newVideoTrack)
//       if (isScreenSharing && pipCameraRef.current) {
//         newVideoTrack.attach(pipCameraRef.current)
//         console.log("✅ New camera track attached to PiP")
//       } else if (!isScreenSharing && videoRef.current) {
//         newVideoTrack.attach(videoRef.current)
//         console.log("✅ New camera track attached to main video")
//       }
//       if (localVideoTrack === previewVideoTrack) {
//         setLocalVideoTrack(newVideoTrack)
//       }
//       if (livekitRoom && livekitConnected) {
//         try {
//           await livekitRoom.localParticipant.publishTrack(newVideoTrack, {
//             source: "camera",
//             name: "teacher_camera",
//           })
//           console.log("✅ New camera track published to LiveKit")
//         } catch (publishErr) {
//           console.warn("⚠️ Failed to publish new camera track:", publishErr)
//         }
//       }
//       return newVideoTrack
//     } catch (err) {
//       console.error("❌ Failed to refresh camera track:", err)
//       throw err
//     }
//   }

//   const ensureParallelStreaming = async () => {
//     try {
//       console.log("🔄 Ensuring parallel streaming of camera and screen...")
//       if (!livekitRoom || !livekitConnected) {
//         console.warn("⚠️ LiveKit room not connected")
//         return
//       }
//       const videoTracks = livekitRoom.localParticipant.videoTracks
//       if (!videoTracks || !videoTracks.values) {
//         console.warn("⚠️ Video tracks not available yet")
//         return
//       }
//       const publishedTracks = Array.from(videoTracks.values())
//       console.log(
//         "📹 Currently published video tracks:",
//         publishedTracks.map((pub) => ({
//           source: pub.source,
//           name: pub.trackName,
//           sid: pub.trackSid,
//         })),
//       )
//       const cameraTrack = localVideoTrack || previewVideoTrack
//       const hasCameraTrack = publishedTracks.some(
//         (pub) => pub.source === "camera" || pub.trackName === "teacher_camera",
//       )
//       if (cameraTrack && !hasCameraTrack) {
//         await livekitRoom.localParticipant.publishTrack(cameraTrack, {
//           source: "camera",
//           name: "teacher_camera",
//         })
//         console.log("✅ Camera track ensured for parallel streaming")
//       }
//       if (isScreenSharing && localScreenTrack) {
//         const hasScreenTrack = publishedTracks.some(
//           (pub) => pub.source === "screen_share" || pub.trackName === "teacher_screen",
//         )
//         if (!hasScreenTrack) {
//           await livekitRoom.localParticipant.publishTrack(localScreenTrack, {
//             source: "screen_share",
//             name: "teacher_screen",
//           })
//           console.log("✅ Screen track ensured for parallel streaming")
//         }
//       }
//       console.log("✅ Parallel streaming ensured - both camera and screen tracks active")
//     } catch (err) {
//       console.error("❌ Failed to ensure parallel streaming:", err)
//     }
//   }

//   // Chat functions
//   const sendChatMessage = async () => {
//     if (!newMessage.trim() || !socketConnected || !sessionId) return
//     const messageData = {
//       session_id: sessionId,
//       message: newMessage.trim(),
//       sender_id: sessionStorage.getItem("userId"),
//       sender_name: sessionStorage.getItem("name") || "Teacher",
//     }
//     try {
//       dispatch(setChatLoading(true))
//       dispatch(setChatError(null))
//       const result = await sendChatMessageMutation(messageData)
//       if (result.data) {
//         console.log("✅ TEACHER: Message sent successfully")
//         setNewMessage("")
//         setTimeout(loadChatHistory, 500)
//       } else if (result.error) {
//         console.error("❌ TEACHER: Failed to send message:", result.error)
//         dispatch(setChatError(result.error.data || "Failed to send message"))
//       }
//     } catch (error) {
//       console.error("❌ TEACHER: Error sending message:", error)
//       dispatch(setChatError(error.message || "Failed to send message"))
//     } finally {
//       dispatch(setChatLoading(false))
//     }
//   }

//   const handleKeyDown = (e) => {
//     if (e.key === "Enter" && !e.shiftKey) {
//       e.preventDefault()
//       sendChatMessage()
//     }
//   }

//   const toggleChat = () => {
//     setIsChatOpen(!isChatOpen)
//     if (!isChatOpen) {
//       setUnreadMessages(0)
//     }
//   }

//   const formatMessageTime = (timestamp) => {
//     return new Date(timestamp).toLocaleTimeString([], {
//       hour: "2-digit",
//       minute: "2-digit",
//     })
//   }

//   // Quiz functions
//   const handleGenerateQuiz = () => {
//     setShowQuizUpload(true)
//   }

//   const handleQuizFileChange = (e) => {
//     const file = e.target.files[0]
//     if (file && file.type === "application/pdf") {
//       setQuizFile(file)
//     } else {
//       alert("Please select a PDF file")
//     }
//   }

//   const handleUploadQuiz = async () => {
//     if (!quizFile || !sessionId) {
//       alert("Please select a PDF file and ensure you're streaming")
//       return
//     }

//     setIsUploadingQuiz(true)
//     try {
//       const formData = new FormData()
//       formData.append("file", quizFile)

//       const response = await uploadCbtPaper(formData).unwrap()

//       if (response.object_id) {
//         // Send quiz notification to all participants via chat
//         const quizMessage = {
//           session_id: sessionId,
//           message: `🎯 QUIZ_START:${response.object_id} Quiz Started! Click 'Open Quiz' to participate. Total Questions: ${response.questions?.length || 0}`,
//           sender_id: sessionStorage.getItem("userId"),
//           sender_name: sessionStorage.getItem("name") || "Teacher",
//         }

//         const result = await sendChatMessageMutation(quizMessage)
//         console.log("Quiz message sent result:", result)

//         if (result.data) {
//           console.log("✅ Quiz message sent successfully")
//           alert(`Quiz generated successfully! Quiz ID: ${response.object_id}\nQuiz message sent to all participants.`)
//         } else if (result.error) {
//           console.error("❌ Failed to send quiz message:", result.error)
//           alert(`Quiz generated successfully! Quiz ID: ${response.object_id}\nWarning: Failed to notify participants via chat.`)
//         }

//         setShowQuizUpload(false)
//         setQuizFile(null)

//         // Refresh chat to show the quiz message
//         setTimeout(loadChatHistory, 500)
//       }
//     } catch (error) {
//       console.error("Quiz upload error:", error)
//       alert("Failed to upload quiz: " + (error.data?.message || error.message || "Unknown error"))
//     } finally {
//       setIsUploadingQuiz(false)
//     }
//   }

//   // Test function to send a test quiz message
//   const sendTestQuizMessage = async () => {
//     if (!sessionId) {
//       alert("Please start streaming first")
//       return
//     }

//     const testQuizMessage = {
//       session_id: sessionId,
//       message: `🎯 QUIZ_START:test123456789 Test Quiz Started! Click 'Open Quiz' to participate. Total Questions: 5`,
//       sender_id: sessionStorage.getItem("userId"),
//       sender_name: sessionStorage.getItem("name") || "Teacher",
//     }

//     try {
//       const result = await sendChatMessageMutation(testQuizMessage)
//       console.log("Test quiz message sent result:", result)
//       if (result.data) {
//         alert("Test quiz message sent successfully!")
//       } else {
//         alert("Failed to send test quiz message")
//       }
//       setTimeout(loadChatHistory, 500)
//     } catch (error) {
//       console.error("Error sending test quiz message:", error)
//       alert("Error sending test quiz message")
//     }
//   }

//   const getRoleColor = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "text-emerald-600"
//       case "student":
//         return "text-blue-600"
//       case "center_counselor":
//         return "text-purple-600"
//       default:
//         return "text-gray-600"
//     }
//   }

//   const getRoleBadge = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "Teacher"
//       case "student":
//         return "Student"
//       case "center_counselor":
//         return "Counselor"
//       default:
//         return "User"
//     }
//   }

//   // Translation Functions
//   const startLiveTranslation = async () => {
//     try {
//       setTranslationStatus("Starting translation...")
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) throw new Error("User not authenticated")

//       // Start translation session with backend
//       const response = await fetch('https://sasthra.in/api/translate/start-session', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({
//           user_id: userId,
//           stream_session_id: sessionId,
//           source_language: sourceLanguage,
//           target_language: "ta" // Default target language for now
//         })
//       })

//       if (!response.ok) throw new Error('Failed to start translation session')
//       const data = await response.json()

//       setTranslationSession(data.session_id)
//       await connectTranslationWebSocket(data.websocket_url)
//       await initializeSpeechRecognition()

//       setIsTranslationEnabled(true)
//       setTranslationStatus("Translation active - listening...")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//       console.error("Translation start error:", error)
//     }
//   }

//   const stopLiveTranslation = async () => {
//     try {
//       setTranslationStatus("Stopping translation...")

//       // Stop speech recognition
//       if (speechRecognition) {
//         speechRecognition.stop()
//         setSpeechRecognition(null)
//       }

//       // Close WebSocket
//       if (translationWebSocket) {
//         translationWebSocket.close()
//         setTranslationWebSocket(null)
//       }
//       if (translationWebSocketRef.current) {
//         translationWebSocketRef.current.close()
//         translationWebSocketRef.current = null
//       }

//       // Stop backend session
//       if (translationSession) {
//         await fetch('https://sasthra.in/api/translate/stop-session', {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ session_id: translationSession })
//         })
//       }

//       setIsTranslationEnabled(false)
//       setTranslationSession(null)
//       setCurrentTranscription("")
//       setTranslationStatus("")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//       console.error("Translation stop error:", error)
//     }
//   }

//   const connectTranslationWebSocket = async (websocketUrl) => {
//     return new Promise((resolve, reject) => {
//       // Fix WebSocket URL if needed
//       let correctedUrl = websocketUrl
//       if (websocketUrl.includes(':8012')) {
//         correctedUrl = websocketUrl.replace(':8012', '')
//       }
//       if (correctedUrl.includes('sasthra.in:8012')) {
//         correctedUrl = correctedUrl.replace('sasthra.in:8012', 'sasthra.in')
//       }

//       console.log('Connecting to WebSocket:', correctedUrl)
//       const ws = new WebSocket(correctedUrl)

//       ws.onopen = () => {
//         setTranslationWebSocket(ws)
//         translationWebSocketRef.current = ws
//         setTranslationStatus("WebSocket connected - ready for speech")
//         resolve()
//       }

//       ws.onmessage = (event) => {
//         const data = JSON.parse(event.data)
//         handleTranslationMessage(data)
//       }

//       ws.onerror = (error) => {
//         setTranslationStatus(`WebSocket error: ${error.message || "Connection failed"}`)
//         reject(error)
//       }

//       ws.onclose = () => {
//         setTranslationWebSocket(null)
//         setTranslationStatus("WebSocket disconnected")
//       }
//     })
//   }

//   const handleTranslationMessage = (data) => {
//     switch (data.type) {
//       case "connection_established":
//         setTranslationStatus("Connected and ready")
//         break
//       case "transcription":
//         setCurrentTranscription(data.text)
//         setTranslationStatus("Transcribing...")
//         break
//       case "translation":
//         setTranslationStatus("Translation completed")
//         break
//       case "translated_audio":
//         setTranslationStatus("Audio generated")
//         break
//       case "error":
//         setTranslationStatus(`Error: ${data.message}`)
//         break
//       default:
//         break
//     }
//   }

//   const initializeSpeechRecognition = async () => {
//     try {
//       // Check if we have shared microphone stream
//       if (!sharedMicrophoneStreamRef.current) {
//         throw new Error("No shared microphone stream available. Please ensure microphone is enabled.")
//       }

//       console.log("🎤 Initializing speech recognition with shared microphone stream...")

//       // Use Web Speech Recognition API with shared microphone stream
//       const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
//       if (!SpeechRecognition) {
//         throw new Error("Speech recognition not supported in this browser")
//       }

//       const recognition = new SpeechRecognition()
//       recognition.continuous = true
//       recognition.interimResults = true

//       // Set language based on sourceLanguage
//       recognition.lang = sourceLanguage === "en" ? "en-US" :
//                         sourceLanguage === "ta" ? "ta-IN" :
//                         sourceLanguage === "hi" ? "hi-IN" :
//                         sourceLanguage === "te" ? "te-IN" :
//                         sourceLanguage === "kn" ? "kn-IN" : "en-US"

//       recognition.onresult = (event) => {
//         const result = event.results[event.results.length - 1]
//         const transcript = result[0].transcript

//         // Update current transcription for display
//         setCurrentTranscription(transcript)

//         // Send final results to backend
//         if (result.isFinal && transcript.trim()) {
//           sendTranscriptionToBackend(transcript.trim())
//         }
//       }

//       recognition.onerror = (event) => {
//         console.error("Speech recognition error:", event.error)
//         if (event.error === 'no-speech') {
//           setTranslationStatus("No speech detected - please speak louder")
//         } else if (event.error === 'audio-capture') {
//           setTranslationStatus("Microphone access error - using shared stream")
//         } else {
//           setTranslationStatus(`Speech recognition error: ${event.error}`)
//         }
//       }

//       recognition.onend = () => {
//         // Restart recognition if translation is still enabled
//         if (isTranslationEnabled) {
//           setTimeout(() => {
//             try {
//               recognition.start()
//             } catch (error) {
//               console.error("Failed to restart speech recognition:", error)
//             }
//           }, 500)
//         }
//       }

//       setSpeechRecognition(recognition)
//       recognition.start()
//       setTranslationStatus("Speech recognition started with shared microphone")

//     } catch (error) {
//       setTranslationStatus(`Speech recognition error: ${error.message}`)
//       console.error("Speech recognition initialization error:", error)
//     }
//   }



//   const sendTranscriptionToBackend = (text) => {
//     const ws = translationWebSocketRef.current || translationWebSocket
//     if (ws && ws.readyState === WebSocket.OPEN) {
//       ws.send(JSON.stringify({
//         type: "transcript",
//         text: text,
//         source_language: sourceLanguage,
//         target_language: "ta", // Default target language
//         session_id: translationSession,
//       }))
//       console.log("Sent transcription to backend:", text)
//     } else {
//       console.warn("WebSocket not ready, cannot send transcription")
//     }
//   }

//   const startStreaming = async () => {
//     try {
//       if (!previewVideoTrack && !localVideoTrack) {
//         console.log("🎥 Camera not initialized, initializing now...")
//         await initializeCameraPreview()
//         await new Promise((resolve) => setTimeout(resolve, 500))
//       }

//       // Ensure shared microphone is available before starting
//       if (!sharedMicrophoneStreamRef.current) {
//         console.log("🎤 Shared microphone not initialized, initializing now...")
//         await initializeSharedMicrophone()
//         await new Promise((resolve) => setTimeout(resolve, 500))
//       }
//       const userId = sessionStorage.getItem("userId")
//       const response = await startStream({
//         userId,
//         sessionId,
//         quality,
//         screenShareEnabled: isScreenSharing,
//       }).unwrap()
//       const backendSessionId = response.session_id
//       if (backendSessionId && backendSessionId !== sessionId) {
//         setSessionId(backendSessionId)
//       }
//       dispatch(setStreamingData({ streamToken: response.livekit_token, roomName: response.roomName }))
//       dispatch(setIsStreaming(true))
//       setLivekitToken(response.livekit_token)
//       setLivekitUrl(response.livekit_url)
//       if (previewVideoTrack) {
//         setLocalVideoTrack(previewVideoTrack)
//       }
//       setStreamStatus("LiveKit stream started successfully")
//     } catch (err) {
//       console.error("❌ Failed to start stream:", err)
//       setStreamStatus("Failed to start stream")
//       dispatch(setError(err.message || "Failed to start stream"))
//     }
//   }

//   const stopStreaming = async () => {
//     try {
//       await stopStream({ sessionId }).unwrap()
//       cleanupAllResources()
//       dispatch(setIsStreaming(false))
//       dispatch(clearStreamingData())
//       setStreamStatus("Ready to start streaming")
//     } catch (err) {
//       console.error("❌ Failed to stop stream:", err)
//       dispatch(setError(err.message || "Failed to stop stream"))
//     }
//   }

//   const cleanupLiveKitResources = () => {
//     if (localVideoTrack) {
//       localVideoTrack.stop()
//       localVideoTrack.detach()
//       setLocalVideoTrack(null)
//     }
//     if (localAudioTrack) {
//       localAudioTrack.stop()
//       localAudioTrack.detach()
//       setLocalAudioTrack(null)
//     }
//     if (previewVideoTrack) {
//       previewVideoTrack.stop()
//       previewVideoTrack.detach()
//       setPreviewVideoTrack(null)
//     }
//     if (localScreenTrack) {
//       localScreenTrack.stop()
//       localScreenTrack.detach()
//       setLocalScreenTrack(null)
//     }
//     if (localScreenAudioTrack) {
//       localScreenAudioTrack.stop()
//       localScreenAudioTrack.detach()
//       setLocalScreenAudioTrack(null)
//     }
//     if (screenStream) {
//       const tracks = screenStream.getTracks()
//       tracks.forEach((track) => track.stop())
//       setScreenStream(null)
//     }
//     if (livekitRoom) {
//       livekitRoom.disconnect()
//       setLivekitRoom(null)
//       setLivekitConnected(false)
//     }
//     livekitRoomRef.current = null
//     console.log("🧹 LiveKit resources cleaned up")
//   }

//   const cleanupAllResources = () => {
//     cleanupLiveKitResources()
//     cleanupTranslationResources()
//     cleanupSharedMicrophone()
//     setIsScreenSharing(false)
//     setParticipants([])
//   }

//   const cleanupTranslationResources = () => {
//     // Stop speech recognition
//     if (speechRecognition) {
//       if (speechRecognition.stop) {
//         // Web Speech Recognition API
//         speechRecognition.stop()
//       }
//       setSpeechRecognition(null)
//     }

//     // Close WebSocket
//     if (translationWebSocket) {
//       translationWebSocket.close()
//       setTranslationWebSocket(null)
//     }
//     if (translationWebSocketRef.current) {
//       translationWebSocketRef.current.close()
//       translationWebSocketRef.current = null
//     }

//     // Reset translation states
//     setIsTranslationEnabled(false)
//     setTranslationSession(null)
//     setCurrentTranscription("")
//     setTranslationStatus("")
//   }

//   const cleanupSharedMicrophone = () => {
//     // Clean up shared microphone stream
//     if (sharedMicrophoneStreamRef.current) {
//       // Clean up audio monitoring
//       if (sharedMicrophoneStreamRef.current.audioLevelInterval) {
//         clearInterval(sharedMicrophoneStreamRef.current.audioLevelInterval)
//       }
//       if (sharedMicrophoneStreamRef.current.audioContext) {
//         sharedMicrophoneStreamRef.current.audioContext.close()
//       }

//       // Stop all tracks
//       sharedMicrophoneStreamRef.current.getTracks().forEach(track => {
//         track.stop()
//       })
//       sharedMicrophoneStreamRef.current = null
//       console.log("🧹 Shared microphone stream cleaned up")
//     }
//   }

//   const startScreenShare = async () => {
//     try {
//       console.log("🖥️ Starting screen share...")
//       setStreamStatus("Starting screen share...")
//       const screenMediaStream = await navigator.mediaDevices.getDisplayMedia({
//         video: { width: { ideal: 1920 }, height: { ideal: 1080 }, frameRate: { ideal: 30 } },
//         audio: true,
//       })
//       setScreenStream(screenMediaStream)
//       const screenTracks = await createLocalScreenTracks({ audio: true, video: true })
//       const screenVideoTrack = screenTracks.find((track) => track.kind === "video")
//       const screenAudioTrack = screenTracks.find((track) => track.kind === "audio")
//       if (screenVideoTrack) {
//         setLocalScreenTrack(screenVideoTrack)
//         if (screenAudioTrack) setLocalScreenAudioTrack(screenAudioTrack)
//         screenVideoTrack.attach(screenVideoRef.current)
//         const videoTrack = screenMediaStream.getVideoTracks()[0]
//         videoTrack?.addEventListener("ended", () => stopScreenShare())
//         setIsScreenSharing(true)
//         setStreamStatus("Screen sharing active")
//         setTimeout(ensureParallelStreaming, 1000)
//         if (previewVideoTrack && pipCameraRef.current) {
//           previewVideoTrack.detach(videoRef.current)
//           previewVideoTrack.attach(pipCameraRef.current)
//         }
//         if (livekitRoom && livekitConnected) {
//           await livekitRoom.localParticipant.publishTrack(screenVideoTrack, { source: "screen_share" })
//           if (screenAudioTrack)
//             await livekitRoom.localParticipant.publishTrack(screenAudioTrack, { source: "screen_share_audio" })
//           const cameraTrack = localVideoTrack || previewVideoTrack
//           if (cameraTrack) {
//             const hasCamera = Array.from(livekitRoom.localParticipant.videoTracks.values()).some(
//               (pub) => pub.source === "camera",
//             )
//             if (!hasCamera) await livekitRoom.localParticipant.publishTrack(cameraTrack, { source: "camera" })
//           }
//         }
//       }
//     } catch (err) {
//       console.error("❌ Error starting screen share:", err)
//       let errorMessage = "Failed to start screen share"
//       if (err.name === "NotAllowedError") errorMessage = "Screen share permission denied. Please allow screen sharing."
//       setStreamStatus(errorMessage)
//       dispatch(setError(errorMessage))
//       setIsScreenSharing(false)
//     }
//   }

//   const stopScreenShare = async () => {
//     try {
//       console.log("🛑 Stopping screen share...")
//       setStreamStatus("Stopping screen share...")
//       if (livekitRoom && livekitConnected) {
//         if (localScreenTrack) await livekitRoom.localParticipant.unpublishTrack(localScreenTrack)
//         if (localScreenAudioTrack) await livekitRoom.localParticipant.unpublishTrack(localScreenAudioTrack)
//       }
//       screenStream?.getTracks().forEach((track) => track.stop())
//       setScreenStream(null)
//       localScreenTrack?.stop()
//       localScreenTrack?.detach()
//       setLocalScreenTrack(null)
//       localScreenAudioTrack?.stop()
//       localScreenAudioTrack?.detach()
//       setLocalScreenAudioTrack(null)
//       setIsScreenSharing(false)
//       setStreamStatus("Screen sharing stopped")
//       if (previewVideoTrack && videoRef.current) {
//         previewVideoTrack.detach(pipCameraRef.current)
//         previewVideoTrack.attach(videoRef.current)
//       }
//     } catch (err) {
//       console.error("❌ Error stopping screen share:", err)
//       setStreamStatus("Error stopping screen share")
//       dispatch(setError(err.message || "Failed to stop screen share"))
//     }
//   }

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
//       <div className="max-w-8xl mx-auto p-4 sm:p-6">
//         {/* Header */}
//         <motion.div
//           initial={{ opacity: 0, y: -20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.5 }}
//           className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-6"
//         >
//           <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
//             <div className="flex items-center space-x-3">
//               <motion.div
//                 whileHover={{ scale: 1.1 }}
//                 className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center"
//               >
//                 <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                   />
//                 </svg>
//               </motion.div>
//               <div>
//                 <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
//                   Live Streaming Studio
//                 </h1>
//                 <p className="text-gray-600 text-sm">Professional teaching platform</p>
//               </div>
//             </div>
//             {isStreaming && (
//               <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
//                 <select
//                   value={quality}
//                   onChange={(e) => setQuality(e.target.value)}
//                   className="px-4 py-2 bg-white/70 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
//                 >
//                   <option value="low">Low Quality</option>
//                   <option value="medium">Medium Quality</option>
//                   <option value="high">High Quality</option>
//                 </select>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={handleGenerateQuiz}
//                   className="px-6 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
//                 >
//                   <span className="flex items-center space-x-2">
//                     <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
//                     </svg>
//                     <span>Generate Quiz</span>
//                   </span>
//                 </motion.button>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={sendTestQuizMessage}
//                   className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium text-sm"
//                 >
//                   <span className="flex items-center space-x-2">
//                     <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
//                     </svg>
//                     <span>Test Quiz</span>
//                   </span>
//                 </motion.button>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={stopStreaming}
//                   className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
//                 >
//                   <span className="flex items-center space-x-2">
//                     <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
//                     <span>Stop Streaming</span>
//                   </span>
//                 </motion.button>
//               </div>
//             )}
//           </div>

//           {/* Translation Controls */}
//           {isStreaming && (
//             <motion.div
//               initial={{ opacity: 0, y: -20 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.1 }}
//               className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl"
//             >
//               <div className="flex items-center justify-between">
//                 <div className="flex items-center space-x-4">
//                   <div className="flex items-center space-x-2">
//                     <svg className="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
//                     </svg>
//                     <span className="text-purple-800 font-medium">Live Translation</span>
//                   </div>

//                   <select
//                     value={sourceLanguage}
//                     onChange={(e) => setSourceLanguage(e.target.value)}
//                     className="px-3 py-1 bg-white border border-purple-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
//                     disabled={isTranslationEnabled}
//                   >
//                     {availableSourceLanguages.map((lang) => (
//                       <option key={lang.code} value={lang.code}>
//                         {lang.flag} {lang.name}
//                       </option>
//                     ))}
//                   </select>

//                   <motion.button
//                     whileHover={{ scale: 1.05 }}
//                     whileTap={{ scale: 0.95 }}
//                     onClick={isTranslationEnabled ? stopLiveTranslation : startLiveTranslation}
//                     className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
//                       isTranslationEnabled
//                         ? "bg-red-500 hover:bg-red-600 text-white"
//                         : "bg-purple-500 hover:bg-purple-600 text-white"
//                     }`}
//                   >
//                     {isTranslationEnabled ? "Stop Translation" : "Start Translation"}
//                   </motion.button>
//                 </div>

//                 {translationStatus && (
//                   <div className="text-sm text-purple-700">
//                     {translationStatus}
//                   </div>
//                 )}
//               </div>

//               {/* Live Transcription Display */}
//               {isTranslationEnabled && (
//                 <div className="mt-3 p-3 bg-white border border-purple-300 rounded-lg">
//                   <div className="flex items-center space-x-2 mb-2">
//                     <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
//                     <span className="text-xs font-medium text-purple-600">LIVE TRANSCRIPTION</span>
//                     <span className="text-xs text-gray-500">({sourceLanguage.toUpperCase()})</span>
//                   </div>
//                   <div className="min-h-[60px] p-2 bg-gray-50 rounded border">
//                     {currentTranscription ? (
//                       <p className="text-sm text-gray-800 leading-relaxed">{currentTranscription}</p>
//                     ) : (
//                       <p className="text-sm text-gray-400 italic">Waiting for speech...</p>
//                     )}
//                   </div>
//                 </div>
//               )}
//             </motion.div>
//           )}

//           <AnimatePresence>
//             {isStreaming && (
//               <motion.div
//                 initial={{ opacity: 0, y: -20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 exit={{ opacity: 0, y: -20 }}
//                 transition={{ duration: 0.3 }}
//                 className="mt-4 p-4 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-xl"
//               >
//                 <div className="flex items-center space-x-3">
//                   <motion.div
//                     animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                     transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
//                     className="w-3 h-3 bg-emerald-500 rounded-full"
//                   />
//                   <span className="text-emerald-800 font-medium">LIVE</span>
//                   <div className="h-4 w-px bg-emerald-300"></div>
//                   <div className="flex items-center space-x-2">
//                     <motion.div
//                       animate={{ scale: microphoneActive ? [1, 1.3, 1] : 1 }}
//                       transition={{ duration: 0.3 }}
//                       className={`w-2 h-2 rounded-full ${microphoneActive ? "bg-red-500" : "bg-gray-400"}`}
//                     />
//                     <span className="text-emerald-700 text-xs">MIC</span>
//                   </div>
//                   <div className="h-4 w-px bg-emerald-300"></div>
//                   <p className="text-emerald-700 text-sm font-medium">{streamStatus}</p>
//                 </div>
//               </motion.div>
//             )}
//           </AnimatePresence>
//         </motion.div>

//         {isStreaming ? (
//           <div className="flex flex-col xl:flex-row gap-6 relative">
//             {/* Main Video Area */}
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               transition={{ duration: 0.5 }}
//               className="flex-grow"
//             >
//               <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden relative">
//                 <div className="bg-gradient-to-r from-gray-900 to-black" style={{ aspectRatio: "16/9" }}>
//                   <AnimatePresence mode="wait">
//                     {!isScreenSharing ? (
//                       <motion.div
//                         key="camera-view"
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         exit={{ opacity: 0 }}
//                         className="flex items-center justify-center h-full text-white p-8 relative"
//                       >
//                         <div className="text-center space-y-6">
//                           <motion.div
//                             initial={{ scale: 0.8 }}
//                             animate={{ scale: 1 }}
//                             className="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto"
//                           >
//                             <svg className="w-10 h-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 strokeWidth={2}
//                                 d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
//                               />
//                             </svg>
//                           </motion.div>
//                           <h3 className="text-xl font-semibold">Ready to Share Your Screen</h3>
//                           <motion.button
//                             whileHover={{ scale: 1.05 }}
//                             whileTap={{ scale: 0.95 }}
//                             className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium"
//                             onClick={startScreenShare}
//                           >
//                             Start Screen Share
//                           </motion.button>
//                         </div>

//                         {/* Live Transcription Overlay - Camera View */}
//                         {isTranslationEnabled && currentTranscription && (
//                           <motion.div
//                             initial={{ opacity: 0, y: 20 }}
//                             animate={{ opacity: 1, y: 0 }}
//                             exit={{ opacity: 0, y: 20 }}
//                             className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
//                           >
//                             <div className="flex items-center space-x-2 mb-2">
//                               <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
//                               <span className="text-xs font-medium text-gray-300">LIVE TRANSCRIPTION</span>
//                             </div>
//                             <p className="text-sm leading-relaxed">{currentTranscription}</p>
//                           </motion.div>
//                         )}
//                       </motion.div>
//                     ) : (
//                       <motion.div
//                         key="screen-share"
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         exit={{ opacity: 0 }}
//                         className="relative w-full h-full"
//                       >
//                         <video
//                           ref={screenVideoRef}
//                           autoPlay
//                           playsInline
//                           className="w-full h-full object-contain bg-black"
//                         />
//                         <motion.div
//                           initial={{ scale: 0.8, opacity: 0 }}
//                           animate={{ scale: 1, opacity: 1 }}
//                           className="absolute bottom-6 right-6 w-56 h-40 bg-black rounded-2xl overflow-hidden border-4 border-white/20 shadow-2xl"
//                         >
//                           <video ref={pipCameraRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                         </motion.div>
//                         <div className="absolute top-6 left-6">
//                           <motion.button
//                             whileHover={{ scale: 1.05 }}
//                             whileTap={{ scale: 0.95 }}
//                             onClick={stopScreenShare}
//                             className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl font-medium"
//                           >
//                             Stop Screen Share
//                           </motion.button>
//                         </div>

//                         {/* Live Transcription Overlay - Screen Share View */}
//                         {isTranslationEnabled && currentTranscription && (
//                           <motion.div
//                             initial={{ opacity: 0, y: 20 }}
//                             animate={{ opacity: 1, y: 0 }}
//                             exit={{ opacity: 0, y: 20 }}
//                             className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
//                           >
//                             <div className="flex items-center space-x-2 mb-2">
//                               <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
//                               <span className="text-xs font-medium text-gray-300">LIVE TRANSCRIPTION</span>
//                             </div>
//                             <p className="text-sm leading-relaxed">{currentTranscription}</p>
//                           </motion.div>
//                         )}
//                       </motion.div>
//                     )}
//                   </AnimatePresence>
//                 </div>
//               </div>
//             </motion.div>

//             {/* Sidebar */}
//             <motion.div
//               initial={{ opacity: 0, x: 20 }}
//               animate={{ opacity: 1, x: 0 }}
//               transition={{ duration: 0.5, delay: 0.1 }}
//               className="w-full xl:w-96 flex-shrink-0 space-y-6"
//             >
//               {/* Camera Preview */}
//               <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
//                 <h3 className="text-lg font-semibold text-gray-800 mb-4">
//                   {isScreenSharing ? "Camera (PiP Mode)" : "Camera Preview"}
//                 </h3>
//                 <motion.div className="bg-black rounded-xl overflow-hidden" style={{ aspectRatio: "4/3" }}>
//                   <video ref={videoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                 </motion.div>
//               </div>

//               {/* Tabbed Info Panel */}
//               <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
//                 {/* Tab Buttons */}
//                 <div className="flex border-b border-gray-200">
//                   <button
//                     onClick={() => setActiveSidebarTab("status")}
//                     className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors ${
//                       activeSidebarTab === "status" ? "text-blue-600 bg-blue-50" : "text-gray-600 hover:bg-gray-50"
//                     }`}
//                   >
//                     <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         strokeWidth={2}
//                         d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
//                       />
//                     </svg>
//                     <span>Status</span>
//                   </button>
//                   <button
//                     onClick={() => setActiveSidebarTab("viewers")}
//                     className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors relative ${
//                       activeSidebarTab === "viewers" ? "text-blue-600 bg-blue-50" : "text-gray-600 hover:bg-gray-50"
//                     }`}
//                   >
//                     <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         strokeWidth={2}
//                         d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
//                       />
//                     </svg>
//                     <span>Viewers</span>
//                     <span className="absolute top-3 right-3 bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-bold">
//                       {joinedViewers.length}
//                     </span>
//                   </button>
//                 </div>

//                 {/* Tab Content */}
//                 <div className="p-6">
//                   <AnimatePresence mode="wait">
//                     {activeSidebarTab === "status" && (
//                       <motion.div key="status" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
//                         <div className="space-y-3">
//                           <div className="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
//                             <span className="text-sm font-medium text-gray-700">LiveKit</span>
//                             <span
//                               className={`text-xs px-2 py-1 rounded-full ${
//                                 livekitConnected ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"
//                               }`}
//                             >
//                               {livekitConnected ? "Connected" : "Disconnected"}
//                             </span>
//                           </div>
//                           <div className="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
//                             <span className="text-sm font-medium text-gray-700">Camera</span>
//                             <span
//                               className={`text-xs px-2 py-1 rounded-full ${
//                                 cameraPermissionGranted
//                                   ? "bg-emerald-100 text-emerald-700"
//                                   : "bg-gray-100 text-gray-600"
//                               }`}
//                             >
//                               {cameraPermissionGranted ? "Ready" : "Not Ready"}
//                             </span>
//                           </div>
//                           <div className="flex items-center justify-between p-3 bg-orange-50 rounded-xl">
//                             <span className="text-sm font-medium text-gray-700">Screen Share</span>
//                             <span
//                               className={`text-xs px-2 py-1 rounded-full ${
//                                 isScreenSharing ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"
//                               }`}
//                             >
//                               {isScreenSharing ? "Active" : "Inactive"}
//                             </span>
//                           </div>
//                           <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
//                             <span className="text-sm font-medium text-gray-700">Chat</span>
//                             <span
//                               className={`text-xs px-2 py-1 rounded-full ${
//                                 socketConnected ? "bg-emerald-100 text-emerald-700" : "bg-gray-100 text-gray-600"
//                               }`}
//                             >
//                               {socketConnected ? "Connected" : "Disconnected"}
//                             </span>
//                           </div>
//                         </div>
//                       </motion.div>
//                     )}
//                     {activeSidebarTab === "viewers" && (
//                       <motion.div key="viewers" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
//                         <div className="space-y-3 max-h-64 overflow-y-auto">
//                           {joinedViewers.length === 0 ? (
//                             <div className="text-center py-8 text-gray-500">No viewers yet.</div>
//                           ) : (
//                             joinedViewers.map((viewer) => (
//                               <div
//                                 key={viewer.viewer_id}
//                                 className="flex items-center justify-between p-3 bg-gray-50 rounded-xl"
//                               >
//                                 <div className="flex items-center space-x-3">
//                                   <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
//                                     {viewer.viewer_name?.charAt(0)?.toUpperCase() || "U"}
//                                   </div>
//                                   <div>
//                                     <p className="text-sm font-medium text-gray-800">{viewer.viewer_name}</p>
//                                     <span
//                                       className={`text-xs px-2 py-0.5 rounded-full ${getRoleColor(
//                                         viewer.user_role,
//                                       )} bg-opacity-10 bg-current`}
//                                     >
//                                       {getRoleBadge(viewer.user_role)}
//                                     </span>
//                                   </div>
//                                 </div>
//                               </div>
//                             ))
//                           )}
//                         </div>
//                       </motion.div>
//                     )}
//                   </AnimatePresence>
//                 </div>
//               </div>
//             </motion.div>

//             {/* Fixed Chat Button - Bottom Right Corner */}
//             <motion.button
//               whileHover={{ scale: 1.1 }}
//               whileTap={{ scale: 0.9 }}
//               onClick={toggleChat}
//               className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white p-4 rounded-full shadow-2xl transition-all duration-200 z-50"
//               title="Toggle Chat"
//             >
//               <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                 />
//               </svg>
//               <AnimatePresence>
//                 {unreadMessages > 0 && !isChatOpen && (
//                   <motion.span
//                     initial={{ scale: 0 }}
//                     animate={{ scale: 1 }}
//                     exit={{ scale: 0 }}
//                     className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold border-2 border-white"
//                   >
//                     {unreadMessages}
//                   </motion.span>
//                 )}
//               </AnimatePresence>
//             </motion.button>

//             {/* Chat Overlay - Fixed Bottom Right */}
//             <AnimatePresence>
//               {isChatOpen && (
//                 <motion.div
//                   initial={{ opacity: 0, y: 20, scale: 0.95 }}
//                   animate={{ opacity: 1, y: 0, scale: 1 }}
//                   exit={{ opacity: 0, y: 20, scale: 0.95 }}
//                   transition={{ duration: 0.3 }}
//                   className="fixed bottom-24 right-6 w-96 h-[500px] bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 flex flex-col z-40"
//                 >
//                   <div className="flex items-center justify-between p-4 border-b border-gray-200">
//                     <h3 className="text-lg font-semibold text-gray-800">Live Chat</h3>
//                     <motion.button
//                       whileHover={{ scale: 1.1, rotate: 90 }}
//                       onClick={toggleChat}
//                       className="text-gray-500 hover:text-gray-800"
//                     >
//                       <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                       </svg>
//                     </motion.button>
//                   </div>
//                   <div className="flex-grow p-4 overflow-y-auto space-y-3">
//                     {chatMessages.length === 0 ? (
//                       <div className="flex items-center justify-center h-full text-center text-gray-500">
//                         No messages yet.
//                       </div>
//                     ) : (
//                       chatMessages.map((message, index) => (
//                         <motion.div
//                           key={`${message.id || "msg"}-${index}`}
//                           initial={{ opacity: 0, y: 10 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           className="bg-white rounded-xl p-3 shadow-sm border"
//                         >
//                           <div className="flex items-start justify-between">
//                             <div className="flex-1">
//                               <span className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}>
//                                 {message.sender_name}
//                               </span>
//                               <p className="text-sm text-gray-800">{message.message}</p>
//                             </div>
//                             <span className="text-xs text-gray-400 ml-2">{formatMessageTime(message.timestamp)}</span>
//                           </div>
//                         </motion.div>
//                       ))
//                     )}
//                   </div>
//                   <div className="p-4 border-t border-gray-200 flex space-x-2">
//                     <input
//                       type="text"
//                       value={newMessage}
//                       onChange={(e) => setNewMessage(e.target.value)}
//                       onKeyDown={handleKeyDown}
//                       placeholder="Type a message..."
//                       className="flex-1 px-4 py-2 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
//                       disabled={!socketConnected}
//                     />
//                     <motion.button
//                       whileHover={{ scale: 1.05 }}
//                       whileTap={{ scale: 0.95 }}
//                       onClick={sendChatMessage}
//                       disabled={!newMessage.trim() || !socketConnected}
//                       className="p-3 bg-blue-600 text-white rounded-xl disabled:bg-gray-400"
//                     >
//                       <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path
//                           strokeLinecap="round"
//                           strokeLinejoin="round"
//                           strokeWidth={2}
//                           d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
//                         />
//                       </svg>
//                     </motion.button>
//                   </div>
//                 </motion.div>
//               )}
//             </AnimatePresence>

//             {/* Quiz Upload Modal */}
//             <AnimatePresence>
//               {showQuizUpload && (
//                 <motion.div
//                   initial={{ opacity: 0 }}
//                   animate={{ opacity: 1 }}
//                   exit={{ opacity: 0 }}
//                   className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
//                   onClick={() => setShowQuizUpload(false)}
//                 >
//                   <motion.div
//                     initial={{ scale: 0.9, opacity: 0 }}
//                     animate={{ scale: 1, opacity: 1 }}
//                     exit={{ scale: 0.9, opacity: 0 }}
//                     className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
//                     onClick={(e) => e.stopPropagation()}
//                   >
//                     <div className="flex items-center justify-between mb-6">
//                       <h3 className="text-2xl font-bold text-gray-800">Generate Quiz</h3>
//                       <motion.button
//                         whileHover={{ scale: 1.1, rotate: 90 }}
//                         onClick={() => setShowQuizUpload(false)}
//                         className="text-gray-500 hover:text-gray-800"
//                       >
//                         <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                         </svg>
//                       </motion.button>
//                     </div>

//                     <div className="space-y-4">
//                       <div>
//                         <label className="block text-sm font-medium text-gray-700 mb-2">
//                           Upload PDF File
//                         </label>
//                         <input
//                           type="file"
//                           accept=".pdf"
//                           onChange={handleQuizFileChange}
//                           className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
//                         />
//                         {quizFile && (
//                           <p className="text-sm text-green-600 mt-2">
//                             ✅ {quizFile.name} selected
//                           </p>
//                         )}
//                       </div>

//                       <div className="flex space-x-3 pt-4">
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           whileTap={{ scale: 0.95 }}
//                           onClick={handleUploadQuiz}
//                           disabled={!quizFile || isUploadingQuiz}
//                           className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed"
//                         >
//                           {isUploadingQuiz ? (
//                             <span className="flex items-center justify-center space-x-2">
//                               <motion.div
//                                 animate={{ rotate: 360 }}
//                                 transition={{ repeat: Infinity, duration: 1 }}
//                                 className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
//                               />
//                               <span>Uploading...</span>
//                             </span>
//                           ) : (
//                             "Generate Quiz"
//                           )}
//                         </motion.button>
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           whileTap={{ scale: 0.95 }}
//                           onClick={() => setShowQuizUpload(false)}
//                           className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl hover:bg-gray-300 transition-all duration-200 font-medium"
//                         >
//                           Cancel
//                         </motion.button>
//                       </div>
//                     </div>
//                   </motion.div>
//                 </motion.div>
//               )}
//             </AnimatePresence>
//           </div>
//         ) : (
//           /* Start Streaming Screen */
//           <motion.div
//             initial={{ opacity: 0, scale: 0.95 }}
//             animate={{ opacity: 1, scale: 1 }}
//             className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12 text-center max-w-2xl mx-auto"
//           >
//             <motion.div
//               whileHover={{ scale: 1.1 }}
//               className="w-24 h-24 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
//             >
//               <svg className="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                 />
//               </svg>
//             </motion.div>
//             <h2 className="text-3xl font-bold text-gray-800 mb-4">Ready to Go Live?</h2>
//             <p className="text-gray-600 mb-8 text-lg">{streamStatus}</p>
//             {error && <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl text-red-700">{error}</div>}
//             <motion.button
//               whileHover={{ scale: 1.05 }}
//               whileTap={{ scale: 0.95 }}
//               onClick={startStreaming}
//               className="px-12 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold text-lg"
//             >
//               Start Live Streaming
//             </motion.button>
//           </motion.div>
//         )}
//       </div>
//     </div>
//   )
// }

// export default TeacherLiveStreaming




///==================================added receiving audio via api SSE-EVENTS
import { useEffect, useState, useRef } from 'react';
import { Room, RoomEvent, Track } from 'livekit-client';
import { motion, AnimatePresence } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
  useUploadCbtPaperMutation,
  useStartTranslationSessionMutation,
  useStopTranslationSessionMutation,
} from './teacherLiveStreaming.slice';

const TeacherLiveStreaming = () => {
  const [startStream, { isLoading: isStartingStream }] = useStartEnhancedStreamMutation();
  const [stopStream, { isLoading: isStoppingStream }] = useStopEnhancedStreamMutation();
  const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation();
  const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery();
  const [uploadCbtPaper] = useUploadCbtPaperMutation();
  const [startTranslationSession, { isLoading: isStartingTranslation }] = useStartTranslationSessionMutation();
  const [stopTranslationSession, { isLoading: isStoppingTranslation }] = useStopTranslationSessionMutation();

  const [room, setRoom] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('');
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [videoTrack, setVideoTrack] = useState(null);
  const [audioTrack, setAudioTrack] = useState(null);
  const [screenTrack, setScreenTrack] = useState(null);
  const videoRef = useRef(null);
  const screenShareRef = useRef(null);

  // Translation states
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationSessionId, setTranslationSessionId] = useState(null);
  const [translationWebSocket, setTranslationWebSocket] = useState(null);
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [targetLanguage, setTargetLanguage] = useState('ta');
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [currentTranslation, setCurrentTranslation] = useState('');
  const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false);
  const [audioQueueLength, setAudioQueueLength] = useState(0);
  const audioContextRef = useRef(null);
  const audioQueueRef = useRef([]);
  const currentAudioSourceRef = useRef(null);
  const currentHtmlAudioRef = useRef(null);
  const isProcessingQueueRef = useRef(false);
  const recognitionRef = useRef(null);

  // Chat states
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [activeSidebarTab, setActiveSidebarTab] = useState('info');

  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' },
  ];

  useEffect(() => {
    return () => {
      if (room) room.disconnect();
      cleanupTranslation();
    };
  }, [room]);

  // Start Web Speech API for transcription
  const startSpeechRecognition = () => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.lang = sourceLanguage;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.continuous = true;

      recognitionRef.current.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map((result) => result[0].transcript)
          .join('');
        setCurrentTranscription(transcript);
        if (translationWebSocket && event.results[event.results.length - 1].isFinal) {
          translationWebSocket.send(JSON.stringify({ type: 'transcript', text: transcript }));
        }
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setCurrentTranscription('Speech recognition error');
      };

      recognitionRef.current.start();
    } else {
      alert('Speech recognition not supported in this browser.');
    }
  };

  // Audio queue management
  const addToAudioQueue = (audioData) => {
    audioQueueRef.current.push(audioData);
    setAudioQueueLength(audioQueueRef.current.length);
    processAudioQueue();
  };

  const processAudioQueue = async () => {
    if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) return;
    isProcessingQueueRef.current = true;
    while (audioQueueRef.current.length > 0) {
      const audioData = audioQueueRef.current.shift();
      setAudioQueueLength(audioQueueRef.current.length);
      await playAudioFromQueue(audioData);
    }
    isProcessingQueueRef.current = false;
    setAudioQueueLength(0);
  };

  const stopCurrentAudio = () => {
    if (currentAudioSourceRef.current) {
      try {
        currentAudioSourceRef.current.stop();
        currentAudioSourceRef.current.disconnect();
      } catch (error) {
        console.warn('⚠️ Error stopping Web Audio source:', error);
      }
      currentAudioSourceRef.current = null;
    }
    if (currentHtmlAudioRef.current) {
      try {
        currentHtmlAudioRef.current.pause();
        currentHtmlAudioRef.current.currentTime = 0;
        if (currentHtmlAudioRef.current.src) {
          URL.revokeObjectURL(currentHtmlAudioRef.current.src);
        }
      } catch (error) {
        console.warn('⚠️ Error stopping HTML5 Audio:', error);
      }
      currentHtmlAudioRef.current = null;
    }
    setIsPlayingTranslatedAudio(false);
  };

  const clearAudioQueue = () => {
    audioQueueRef.current = [];
    setAudioQueueLength(0);
    stopCurrentAudio();
    isProcessingQueueRef.current = false;
  };

  const playAudioFromQueue = async (audioDataBase64) => {
    return new Promise(async (resolve) => {
      try {
        if (!audioDataBase64) {
          setIsPlayingTranslatedAudio(false);
          resolve();
          return;
        }
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        }
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
        try {
          const binaryString = atob(audioDataBase64);
          const audioData = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            audioData[i] = binaryString.charCodeAt(i);
          }
          const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer);
          const source = audioContextRef.current.createBufferSource();
          source.buffer = audioBuffer;
          source.connect(audioContextRef.current.destination);
          currentAudioSourceRef.current = source;
          setIsPlayingTranslatedAudio(true);
          source.onended = () => {
            setIsPlayingTranslatedAudio(false);
            currentAudioSourceRef.current = null;
            resolve();
          };
          source.start();
        } catch (webAudioError) {
          try {
            const audioBlob = new Blob(
              [
                new Uint8Array(
                  atob(audioDataBase64)
                    .split('')
                    .map((c) => c.charCodeAt(0)),
                ),
              ],
              { type: 'audio/wav' },
            );
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);
            currentHtmlAudioRef.current = audio;
            setIsPlayingTranslatedAudio(true);
            audio.onended = () => {
              setIsPlayingTranslatedAudio(false);
              currentHtmlAudioRef.current = null;
              URL.revokeObjectURL(audioUrl);
              resolve();
            };
            audio.onerror = () => {
              setIsPlayingTranslatedAudio(false);
              currentHtmlAudioRef.current = null;
              URL.revokeObjectURL(audioUrl);
              resolve();
            };
            await audio.play();
          } catch (htmlAudioError) {
            setIsPlayingTranslatedAudio(false);
            resolve();
          }
        }
      } catch (error) {
        setIsPlayingTranslatedAudio(false);
        resolve();
      }
    });
  };

  const cleanupTranslation = () => {
    if (translationWebSocket) {
      translationWebSocket.close();
      setTranslationWebSocket(null);
    }
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      recognitionRef.current = null;
    }
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    clearAudioQueue();
    setIsTranslating(false);
    setTranslationSessionId(null);
    setCurrentTranscription('');
    setCurrentTranslation('');
  };

  const startTranslation = async () => {
    try {
      const response = await startTranslationSession({
        source_language: sourceLanguage,
        target_language: targetLanguage,
      }).unwrap();
      if (response.success && response.session_id && response.websocket_url) {
        setTranslationSessionId(response.session_id);
        connectTranslationWebSocket(response.websocket_url);
        startSpeechRecognition();
        setIsTranslating(true);

        // Send translation session ID via chat
        const messageData = {
          session_id: room?.name,
          message: `TRANSLATION_SESSION:${response.session_id}`,
          sender_id: sessionStorage.getItem('userId'),
          sender_name: sessionStorage.getItem('name') || 'Teacher',
        };
        await sendChatMessage(messageData).unwrap();
        toast.success('Translation session ID shared via chat');
      } else {
        toast.error('Failed to start translation session');
      }
    } catch (error) {
      toast.error('Error starting translation: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const stopTranslation = async () => {
    try {
      if (translationSessionId) {
        await stopTranslationSession({ session_id: translationSessionId }).unwrap();
      }
      cleanupTranslation();
      toast.success('Translation stopped');
    } catch (error) {
      toast.error('Error stopping translation: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const connectTranslationWebSocket = (websocketUrl) => {
    const ws = new WebSocket(websocketUrl);
    ws.onopen = () => {
      console.log('Translation WebSocket connected');
      toast.success('Translation WebSocket connected');
    };
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'translation') {
        setCurrentTranslation(data.translated_text);
      } else if (data.type === 'translated_audio') {
        addToAudioQueue(data.audio_data);
      }
    };
    ws.onerror = (error) => {
      console.error('Translation WebSocket error:', error);
      toast.error('Translation WebSocket error');
      cleanupTranslation();
    };
    ws.onclose = () => {
      console.log('Translation WebSocket closed');
      cleanupTranslation();
    };
    setTranslationWebSocket(ws);
  };

  const startStreaming = async () => {
    try {
      const userId = sessionStorage.getItem('userId');
      const response = await startStream({ user_id: userId }).unwrap();
      if (response.token && response.livekit_url) {
        setIsStreaming(true);
        setConnectionStatus('Connecting...');
        const newRoom = new Room();
        setRoom(newRoom);
        await connectToLiveKitRoom(newRoom, response.token, response.livekit_url);
      } else {
        toast.error('Invalid stream response');
      }
    } catch (error) {
      toast.error('Failed to start stream: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const stopStreaming = async () => {
    try {
      const userId = sessionStorage.getItem('userId');
      await stopStream({ user_id: userId, room_name: room?.name }).unwrap();
      if (room) {
        room.disconnect();
        setRoom(null);
      }
      setIsStreaming(false);
      setParticipants([]);
      setVideoTrack(null);
      setAudioTrack(null);
      setScreenTrack(null);
      setConnectionStatus('');
      setIsCameraOn(false);
      setIsMicOn(false);
      setIsScreenSharing(false);
      cleanupTranslation();
      toast.success('Stream stopped successfully');
    } catch (error) {
      toast.error('Error stopping stream: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const connectToLiveKitRoom = async (room, token, url) => {
    room.on(RoomEvent.Connected, () => {
      setConnectionStatus('Connected');
      toast.success('Connected to streaming room');
    });
    room.on(RoomEvent.Disconnected, () => {
      setConnectionStatus('Disconnected');
      setIsStreaming(false);
      toast.info('Disconnected from streaming room');
    });
    room.on(RoomEvent.ParticipantConnected, (participant) => {
      setParticipants((prev) => [...prev, participant]);
    });
    room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity));
    });
    room.on(RoomEvent.LocalTrackPublished, (trackPublication) => {
      const track = trackPublication.track;
      if (track.kind === 'video') {
        if (track.source === Track.Source.Camera) {
          setVideoTrack(track);
          setIsCameraOn(true);
          if (videoRef.current) track.attach(videoRef.current);
        } else if (track.source === Track.Source.ScreenShare) {
          setScreenTrack(track);
          setIsScreenSharing(true);
          if (screenShareRef.current) track.attach(screenShareRef.current);
        }
      } else if (track.kind === 'audio') {
        setAudioTrack(track);
        setIsMicOn(true);
      }
    });
    room.on(RoomEvent.LocalTrackUnpublished, (trackPublication) => {
      const track = trackPublication.track;
      if (track.kind === 'video') {
        if (track.source === Track.Source.Camera) {
          setVideoTrack(null);
          setIsCameraOn(false);
        } else if (track.source === Track.Source.ScreenShare) {
          setScreenTrack(null);
          setIsScreenSharing(false);
        }
      } else if (track.kind === 'audio') {
        setAudioTrack(null);
        setIsMicOn(false);
      }
    });
    await room.connect(url, token);
  };

  const toggleCamera = async () => {
    if (!room) return;
    if (isCameraOn) {
      if (videoTrack) {
        await room.localParticipant.unpublishTrack(videoTrack);
        videoTrack.stop();
        setVideoTrack(null);
        setIsCameraOn(false);
      }
    } else {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const videoTrack = stream.getVideoTracks()[0];
      await room.localParticipant.publishTrack(videoTrack);
      setVideoTrack(videoTrack);
      setIsCameraOn(true);
      if (videoRef.current) videoTrack.attach(videoRef.current);
    }
  };

  const toggleMicrophone = async () => {
    if (!room) return;
    if (isMicOn) {
      if (audioTrack) {
        await room.localParticipant.unpublishTrack(audioTrack);
        audioTrack.stop();
        setAudioTrack(null);
        setIsMicOn(false);
      }
    } else {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const audioTrack = stream.getAudioTracks()[0];
      await room.localParticipant.publishTrack(audioTrack);
      setAudioTrack(audioTrack);
      setIsMicOn(true);
    }
  };

  const toggleScreenShare = async () => {
    if (!room) return;
    if (isScreenSharing) {
      if (screenTrack) {
        await room.localParticipant.unpublishTrack(screenTrack);
        screenTrack.stop();
        setScreenTrack(null);
        setIsScreenSharing(false);
      }
    } else {
      const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });
      const screenTrack = stream.getVideoTracks()[0];
      await room.localParticipant.publishTrack(screenTrack);
      setScreenTrack(screenTrack);
      setIsScreenSharing(true);
      if (screenShareRef.current) screenTrack.attach(screenShareRef.current);
    }
  };

  const handleSendChatMessage = async () => {
    if (!newMessage.trim() || !room) return;
    const messageData = {
      session_id: room.name,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem('userId'),
      sender_name: sessionStorage.getItem('name') || 'Teacher',
    };
    try {
      await sendChatMessage(messageData).unwrap();
      setNewMessage('');
      setTimeout(() => getChatHistory(room.name), 500);
    } catch (error) {
      toast.error('Error sending message: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendChatMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) setUnreadMessages(0);
  };

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'kota_teacher':
      case 'faculty':
        return 'text-emerald-500';
      case 'student':
        return 'text-blue-500';
      case 'center_counselor':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };

  useEffect(() => {
    if (isStreaming && room) {
      getChatHistory(room.name);
      const pollInterval = setInterval(() => getChatHistory(room.name), 2000);
      return () => clearInterval(pollInterval);
    }
  }, [isStreaming, room, getChatHistory]);

  useEffect(() => {
    if (chatHistoryData) {
      setChatMessages(chatHistoryData);
      if (!isChatOpen) {
        const newMessages = chatHistoryData.length - chatMessages.length;
        if (newMessages > 0) setUnreadMessages((prev) => prev + newMessages);
      }
    }
  }, [chatHistoryData]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
    >
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={isStreaming ? stopStreaming : startStreaming}
              className={`${
                isStreaming ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'
              } text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2`}
              disabled={isStartingStream || isStoppingStream}
            >
              {isStreaming ? (
                <>
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span>{isStoppingStream ? 'Stopping...' : 'Stop Stream'}</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14" />
                  </svg>
                  <span>{isStartingStream ? 'Starting...' : 'Start Stream'}</span>
                </>
              )}
            </motion.button>
            <div className="border-l border-gray-600 pl-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                Teacher Live Stream
              </h1>
              <p className="text-sm text-gray-300">Session: {room?.name || 'Not started'}</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
              <motion.div
                animate={{ scale: connectionStatus === 'Connected' ? [1, 1.2, 1] : 1 }}
                transition={{ repeat: connectionStatus === 'Connected' ? Infinity : 0, duration: 1.5 }}
                className={`w-3 h-3 rounded-full mr-3 ${connectionStatus === 'Connected' ? 'bg-green-400' : 'bg-red-400'}`}
              ></motion.div>
              <span className="text-sm font-medium">{connectionStatus || 'Disconnected'}</span>
            </div>
            <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
              <svg className="w-4 h-4 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
              <span className="text-sm font-medium">{participants.length} Viewers</span>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
        >
          <div className="w-full h-full flex items-center justify-center relative">
            {isScreenSharing ? (
              <video ref={screenShareRef} autoPlay playsInline className="w-full h-full object-contain" />
            ) : (
              <video ref={videoRef} autoPlay playsInline className="w-full h-full object-contain" />
            )}
            {!isStreaming && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/70 to-black/70"
              >
                <div className="text-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ repeat: Infinity, duration: 2, ease: 'linear' }}
                    className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto mb-4"
                  ></motion.div>
                  <motion.p
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="text-xl font-semibold"
                  >
                    Stream not started
                  </motion.p>
                </div>
              </motion.div>
            )}
          </div>
          {isCameraOn && isScreenSharing && (
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.4 }}
              className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
            >
              <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />
              <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                Camera
              </div>
            </motion.div>
          )}
          {!isChatOpen && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleChat}
              className="absolute bottom-6 left-6 bg-indigo-500 hover:bg-indigo-600 text-white p-3 rounded-full shadow-lg"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                />
              </svg>
              {unreadMessages > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                >
                  {unreadMessages}
                </motion.span>
              )}
            </motion.button>
          )}
        </motion.div>

        <AnimatePresence>
          {isChatOpen && (
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="absolute right-0 top-0 bottom-0 w-96 bg-gray-800 text-white shadow-lg border-l border-gray-700 z-10"
            >
              <div className="p-4 border-b border-gray-700 flex justify-between items-center">
                <h3 className="text-lg font-semibold">Live Chat</h3>
                <button onClick={toggleChat} className="text-gray-400 hover:text-white">
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="h-[calc(100%-120px)] overflow-y-auto p-4">
                {isLoadingHistory ? (
                  <div className="flex items-center justify-center h-full">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 1 }}
                      className="w-6 h-6 border-2 border-indigo-200 border-t-indigo-500 rounded-full"
                    ></motion.div>
                  </div>
                ) : chatMessages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    No messages yet
                  </div>
                ) : (
                  <AnimatePresence>
                    {chatMessages.map((message, index) => (
                      <motion.div
                        key={`${message.id || 'msg'}-${index}-${message.timestamp || Date.now()}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="p-2 hover:bg-gray-700/30 rounded mb-2"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className={`text-sm font-medium ${getRoleColor(message.sender_role)}`}>
                                {message.sender_name}
                              </span>
                              <span className="text-xs bg-gray-700 px-2 py-1 rounded">{message.sender_role}</span>
                            </div>
                            <p className="text-sm text-gray-200">{message.message}</p>
                          </div>
                          <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-800 border-t border-gray-700">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Type a message..."
                    className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
                  />
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleSendChatMessage}
                    disabled={!newMessage.trim() || isSendingMessage}
                    className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
                  >
                    {isSendingMessage ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ repeat: Infinity, duration: 1 }}
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                      ></motion.div>
                    ) : (
                      'Send'
                    )}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-20 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
        >
          <div className="flex flex-col items-center py-4 space-y-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('info')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'info' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('controls')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'controls' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('participants')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'participants' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('translation')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'translation' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
            </motion.button>
          </div>
        </motion.div>

        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-80 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
        >
          <div className="p-6 space-y-6">
            {activeSidebarTab === 'info' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Stream Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                    <span className="text-gray-300">Session ID:</span>
                    <span className="font-medium text-indigo-400">{room?.name || 'Not started'}</span>
                  </div>
                  <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                    <span className="text-gray-300">Status:</span>
                    <span className="font-medium text-indigo-400">{connectionStatus || 'Disconnected'}</span>
                  </div>
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'controls' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Stream Controls</h3>
                <div className="space-y-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleCamera}
                    className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold ${
                      isCameraOn ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-500 hover:bg-indigo-600'
                    } text-white`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {isCameraOn ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14" />
                      )}
                    </svg>
                    <span>{isCameraOn ? 'Turn Off Camera' : 'Turn On Camera'}</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleMicrophone}
                    className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold ${
                      isMicOn ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-500 hover:bg-indigo-600'
                    } text-white`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {isMicOn ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 1v6m0 6v6m-6-3h12" />
                      )}
                    </svg>
                    <span>{isMicOn ? 'Turn Off Mic' : 'Turn On Mic'}</span>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={toggleScreenShare}
                    className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold ${
                      isScreenSharing ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-500 hover:bg-indigo-600'
                    } text-white`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      {isScreenSharing ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      )}
                    </svg>
                    <span>{isScreenSharing ? 'Stop Screen Share' : 'Share Screen'}</span>
                  </motion.button>
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'participants' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Participants ({participants.length})</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <AnimatePresence>
                    {participants.map((participant, index) => (
                      <motion.div
                        key={participant.identity || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="flex items-center bg-indigo-500/10 p-2 rounded"
                      >
                        <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                          {participant.identity?.charAt(0)?.toUpperCase() || 'U'}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-indigo-400">{participant.identity || 'Unknown'}</div>
                          <div className="text-xs text-gray-400">Viewer</div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'translation' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Translation</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 font-medium">Translation</span>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={isTranslating ? stopTranslation : startTranslation}
                      className={`px-4 py-2 rounded-lg font-semibold ${
                        isTranslating ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-500 hover:bg-indigo-600'
                      } text-white`}
                      disabled={isStartingTranslation || isStoppingTranslation}
                    >
                      {isTranslating ? (isStoppingTranslation ? 'Stopping...' : 'Stop') : (isStartingTranslation ? 'Starting...' : 'Start')}
                    </motion.button>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">From</label>
                    <select
                      value={sourceLanguage}
                      onChange={(e) => setSourceLanguage(e.target.value)}
                      disabled={isTranslating}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
                    >
                      {availableLanguages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">To</label>
                    <select
                      value={targetLanguage}
                      onChange={(e) => setTargetLanguage(e.target.value)}
                      disabled={isTranslating}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
                    >
                      {availableLanguages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                      <motion.div
                        animate={{ scale: isTranslating ? [1, 1.2, 1] : 1 }}
                        transition={{ repeat: isTranslating ? Infinity : 0, duration: 1.5 }}
                        className={`w-3 h-3 rounded-full mr-2 ${isTranslating ? 'bg-green-400' : 'bg-gray-400'}`}
                      ></motion.div>
                      <span className="text-sm">{isTranslating ? 'Translating' : 'Translation Inactive'}</span>
                    </div>
                    {isPlayingTranslatedAudio && (
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ repeat: Infinity, duration: 1 }}
                          className="w-3 h-3 bg-indigo-400 rounded-full mr-2"
                        ></motion.div>
                        <span className="text-sm">Playing Translated Audio</span>
                      </div>
                    )}
                    {audioQueueLength > 0 && (
                      <div className="flex items-center bg-yellow-500/10 p-2 rounded">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ repeat: Infinity, duration: 1 }}
                          className="w-3 h-3 bg-yellow-400 rounded-full mr-2"
                        ></motion.div>
                        <span className="text-sm">Audio Queue: {audioQueueLength} pending</span>
                      </div>
                    )}
                  </div>
                  {currentTranscription && (
                    <div className="bg-gray-700/50 p-2 rounded text-sm">
                      <span className="font-medium">Transcription:</span> {currentTranscription}
                    </div>
                  )}
                  {currentTranslation && (
                    <div className="bg-gray-700/50 p-2 rounded text-sm">
                      <span className="font-medium">Translation:</span> {currentTranslation}
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </motion.div>
  );
};

export default TeacherLiveStreaming;