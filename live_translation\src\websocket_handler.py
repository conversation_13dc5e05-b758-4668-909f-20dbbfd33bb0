import asyncio
import logging
from typing import Dict, List
from fastapi import WebSocket
import json

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections for real-time translation"""
    
    def __init__(self):
        # Store active connections by session_id
        self.active_connections: Dict[str, WebSocket] = {}
        # Store connection metadata
        self.connection_metadata: Dict[str, dict] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """Accept a new WebSocket connection"""
        try:
            await websocket.accept()
            self.active_connections[session_id] = websocket
            self.connection_metadata[session_id] = {
                "connected_at": asyncio.get_event_loop().time(),
                "messages_sent": 0,
                "messages_received": 0,
                "last_activity": asyncio.get_event_loop().time()
            }
            logger.info(f"WebSocket connected for session {session_id}")
            
        except Exception as e:
            logger.error(f"Error connecting WebSocket for session {session_id}: {str(e)}")
            raise
    
    async def disconnect(self, websocket: WebSocket):
        """Disconnect a WebSocket connection"""
        try:
            # Find session_id for this websocket
            session_id = None
            for sid, ws in self.active_connections.items():
                if ws == websocket:
                    session_id = sid
                    break
            
            if session_id:
                # Remove from active connections
                if session_id in self.active_connections:
                    del self.active_connections[session_id]
                
                if session_id in self.connection_metadata:
                    del self.connection_metadata[session_id]
                
                logger.info(f"WebSocket disconnected for session {session_id}")
            
            # Close the websocket if it's still open
            if websocket.client_state.name != "DISCONNECTED":
                await websocket.close()
                
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {str(e)}")
    
    async def disconnect_all(self):
        """Disconnect all active WebSocket connections"""
        try:
            for session_id, websocket in list(self.active_connections.items()):
                await self.disconnect(websocket)
            
            logger.info("All WebSocket connections disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting all WebSockets: {str(e)}")
    
    async def send_message(self, session_id: str, message: dict):
        """Send a message to a specific session"""
        try:
            if session_id in self.active_connections:
                websocket = self.active_connections[session_id]
                await websocket.send_json(message)
                
                # Update metadata
                if session_id in self.connection_metadata:
                    self.connection_metadata[session_id]["messages_sent"] += 1
                    self.connection_metadata[session_id]["last_activity"] = asyncio.get_event_loop().time()
                
                logger.debug(f"Message sent to session {session_id}: {message.get('type', 'unknown')}")
                
            else:
                logger.warning(f"Attempted to send message to non-existent session {session_id}")
                
        except Exception as e:
            logger.error(f"Error sending message to session {session_id}: {str(e)}")
            # Remove broken connection
            if session_id in self.active_connections:
                await self.disconnect(self.active_connections[session_id])
    
    async def send_binary(self, session_id: str, data: bytes):
        """Send binary data to a specific session"""
        try:
            if session_id in self.active_connections:
                websocket = self.active_connections[session_id]
                await websocket.send_bytes(data)
                
                # Update metadata
                if session_id in self.connection_metadata:
                    self.connection_metadata[session_id]["messages_sent"] += 1
                    self.connection_metadata[session_id]["last_activity"] = asyncio.get_event_loop().time()
                
                logger.debug(f"Binary data sent to session {session_id}: {len(data)} bytes")
                
            else:
                logger.warning(f"Attempted to send binary data to non-existent session {session_id}")
                
        except Exception as e:
            logger.error(f"Error sending binary data to session {session_id}: {str(e)}")
            # Remove broken connection
            if session_id in self.active_connections:
                await self.disconnect(self.active_connections[session_id])
    
    async def broadcast_message(self, message: dict, exclude_sessions: List[str] = None):
        """Broadcast a message to all active connections"""
        try:
            exclude_sessions = exclude_sessions or []
            
            for session_id, websocket in list(self.active_connections.items()):
                if session_id not in exclude_sessions:
                    try:
                        await websocket.send_json(message)
                        
                        # Update metadata
                        if session_id in self.connection_metadata:
                            self.connection_metadata[session_id]["messages_sent"] += 1
                            self.connection_metadata[session_id]["last_activity"] = asyncio.get_event_loop().time()
                            
                    except Exception as e:
                        logger.error(f"Error broadcasting to session {session_id}: {str(e)}")
                        # Remove broken connection
                        await self.disconnect(websocket)
            
            logger.debug(f"Message broadcasted to {len(self.active_connections)} sessions")
            
        except Exception as e:
            logger.error(f"Error broadcasting message: {str(e)}")
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        return list(self.active_connections.keys())
    
    def get_connection_count(self) -> int:
        """Get number of active connections"""
        return len(self.active_connections)
    
    def get_session_metadata(self, session_id: str) -> dict:
        """Get metadata for a specific session"""
        return self.connection_metadata.get(session_id, {})
    
    def is_session_active(self, session_id: str) -> bool:
        """Check if a session is active"""
        return session_id in self.active_connections
    
    async def ping_all_connections(self):
        """Send ping to all connections to check health"""
        try:
            ping_message = {
                "type": "ping",
                "timestamp": asyncio.get_event_loop().time()
            }
            
            for session_id, websocket in list(self.active_connections.items()):
                try:
                    await websocket.send_json(ping_message)
                except Exception as e:
                    logger.warning(f"Ping failed for session {session_id}: {str(e)}")
                    # Remove broken connection
                    await self.disconnect(websocket)
            
            logger.debug(f"Ping sent to {len(self.active_connections)} active connections")
            
        except Exception as e:
            logger.error(f"Error pinging connections: {str(e)}")
    
    async def cleanup_inactive_connections(self, timeout_seconds: int = 300):
        """Clean up connections that have been inactive for too long"""
        try:
            current_time = asyncio.get_event_loop().time()
            inactive_sessions = []
            
            for session_id, metadata in self.connection_metadata.items():
                if current_time - metadata.get("last_activity", 0) > timeout_seconds:
                    inactive_sessions.append(session_id)
            
            for session_id in inactive_sessions:
                if session_id in self.active_connections:
                    logger.info(f"Cleaning up inactive session {session_id}")
                    await self.disconnect(self.active_connections[session_id])
            
            if inactive_sessions:
                logger.info(f"Cleaned up {len(inactive_sessions)} inactive connections")
                
        except Exception as e:
            logger.error(f"Error cleaning up inactive connections: {str(e)}")
    
    def get_connection_stats(self) -> dict:
        """Get statistics about all connections"""
        try:
            total_connections = len(self.active_connections)
            total_messages_sent = sum(
                metadata.get("messages_sent", 0) 
                for metadata in self.connection_metadata.values()
            )
            total_messages_received = sum(
                metadata.get("messages_received", 0) 
                for metadata in self.connection_metadata.values()
            )
            
            return {
                "total_connections": total_connections,
                "total_messages_sent": total_messages_sent,
                "total_messages_received": total_messages_received,
                "active_sessions": list(self.active_connections.keys())
            }
            
        except Exception as e:
            logger.error(f"Error getting connection stats: {str(e)}")
            return {
                "total_connections": 0,
                "total_messages_sent": 0,
                "total_messages_received": 0,
                "active_sessions": []
            }
