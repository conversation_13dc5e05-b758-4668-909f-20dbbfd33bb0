import { useState, useMemo, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Search,
  X,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  FileText,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Table2
} from 'lucide-react';
import Input from '../Field/Input';
import Button from '../Field/Button';

const DOTS = '...';

export default function Table({
  title = 'Table',
  titleIcon = <Table2 className="h-5 w-5" />,
  buttonName = 'Add New',
  onAddNew,
  header = [],
  data = [],
  searchBy = [],
  searchPlaceholder = 'Search...',
  onView,
  onEdit,
  onDelete,
  onPdf,
  customHeader,
  customColumn,
  itemsPerPage = 10,
  loading = false,
  emptyMessage = 'No records found'
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [filteredData, setFilteredData] = useState(data);
  const [pageSize, setPageSize] = useState(itemsPerPage);
  const [showActionsMenu, setShowActionsMenu] = useState(null);
  const [showPageSizeMenu, setShowPageSizeMenu] = useState(false);

  const visibleHeaders = useMemo(() => header.filter((col) => col.show), [header]);

  const totalItems = filteredData?.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    } else if (totalPages === 0 && data.length > 0) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage, data?.length]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!search.trim()) {
        setFilteredData(data);
        return;
      }
      const lowercasedSearchTerm = search.toLowerCase();
      const filtered = data.filter((item) => {
        if (searchBy && searchBy.length > 0) {
          return searchBy.some((key) => {
            const value = key
              .split('.')
              .reduce((o, i) => (o && typeof o === 'object' ? o[i] : undefined), item);
            return (
              value !== undefined &&
              value !== null &&
              String(value).toLowerCase().includes(lowercasedSearchTerm)
            );
          });
        } else {
          return visibleHeaders.some((col) => {
            if (col.data) {
              const value = item[col.data];
              return (
                value !== undefined &&
                value !== null &&
                String(value).toLowerCase().includes(lowercasedSearchTerm)
              );
            }
            return false;
          });
        }
      });
      setFilteredData(filtered);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [search, data, visibleHeaders, searchBy]);

  useEffect(() => {
    setCurrentPage(1);
  }, [search]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredData?.slice(startIndex, endIndex);
  }, [filteredData, currentPage, pageSize]);

  const handlePageChange = useCallback(
    (page) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  const handleSearchChange = useCallback((e) => {
    setSearch(e.target.value);
  }, []);

  const clearSearch = useCallback(() => {
    setSearch('');
  }, []);

  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === 'ArrowLeft') {
        handlePageChange(currentPage - 1);
      } else if (e.key === 'ArrowRight') {
        handlePageChange(currentPage + 1);
      }
    },
    [currentPage, handlePageChange]
  );

  useEffect(() => {
    const handleClickOutside = () => {
      setShowActionsMenu(null);
      setShowPageSizeMenu(false);
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const startIndex = (currentPage - 1) * pageSize;
  // const endIndex = Math.min(startIndex + pageSize, totalItems);

  const paginationRange = useMemo(() => {
    const totalPageNumbers = 7;
    if (totalPages <= totalPageNumbers) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    const siblingCount = 1;
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2;
    const firstPageIndex = 1;
    const lastPageIndex = totalPages;
    if (!shouldShowLeftDots && shouldShowRightDots) {
      let leftItemCount = 3 + 2 * siblingCount;
      let leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
      return [...leftRange, DOTS, totalPages];
    }
    if (shouldShowLeftDots && !shouldShowRightDots) {
      let rightItemCount = 3 + 2 * siblingCount;
      let rightRange = Array.from(
        { length: rightItemCount },
        (_, i) => totalPages - rightItemCount + 1 + i
      );
      return [firstPageIndex, DOTS, ...rightRange];
    }
    if (shouldShowLeftDots && shouldShowRightDots) {
      let middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i
      );
      return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }
  }, [totalPages, currentPage]);

  const ActionButton = ({ onClick, icon, label, variant = 'ghost', disabled = false }) => (
    <Button
      onClick={onClick}
      disabled={disabled}
      name={icon}
      className={`h-8 w-8 p-0 rounded hover:bg-gray-100 ${variant === 'destructive' ? 'text-red-600 hover:text-red-700 hover:bg-red-50' : disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-800'}`}
      title={label}
    />
  );
  const CustomDropdown = ({ trigger, children, isOpen, onToggle }) => (
    <div className="relative" onClick={(e) => e.stopPropagation()}>
      {' '}
      <div onClick={onToggle}>{trigger}</div>{' '}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          {' '}
          <div className="py-1">{children}</div>{' '}
        </div>
      )}{' '}
    </div>
  );
  const DropdownItem = ({ onClick, children, disabled = false }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center ${disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700'}`}>
      {' '}
      {children}{' '}
    </button>
  );
  const PageSizeSelector = () => (
    <div className="relative" onClick={(e) => e.stopPropagation()}>
      {' '}
      <Button
        onClick={() => setShowPageSizeMenu(!showPageSizeMenu)}
        name={pageSize}
        className="w-16 h-8 border rounded text-sm"
      />{' '}
      {showPageSizeMenu && (
        <div className="absolute bottom-full mb-1 left-0 bg-white border rounded shadow-lg z-50">
          {' '}
          {[5, 10, 20, 50].map((size) => (
            <button
              key={size}
              onClick={() => {
                setPageSize(size);
                setCurrentPage(1);
                setShowPageSizeMenu(false);
              }}
              className="block w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
              {' '}
              {size}{' '}
            </button>
          ))}{' '}
        </div>
      )}{' '}
    </div>
  );
  const renderActions = (item, index) => {
    const actions = [];
    if (onView)
      actions.push({
        key: 'view',
        onClick: () => onView(item),
        icon:
          item.uploadstatus === false ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          ),
        label: 'View',
        disabled: item.uploadstatus === false
      });
    if (onEdit)
      actions.push({
        key: 'edit',
        onClick: () => onEdit(item),
        icon: <Edit className="h-4 w-4" />,
        label: 'Edit'
      });
    if (onPdf)
      actions.push({
        key: 'pdf',
        onClick: () => onPdf(item),
        icon: <FileText className="h-4 w-4" />,
        label: 'Download PDF'
      });
    if (onDelete)
      actions.push({
        key: 'delete',
        onClick: () => onDelete(item),
        icon: <Trash2 className="h-4 w-4" />,
        label: 'Delete',
        variant: 'destructive'
      });
    if (actions.length <= 2) {
      return (
        <div className="flex items-center gap-1">
          {actions.map((action) => (
            <ActionButton
              key={action.key}
              onClick={action.onClick}
              icon={action.icon}
              label={action.label}
              variant={action.variant}
              disabled={action.disabled}
            />
          ))}
        </div>
      );
    }
    return (
      <CustomDropdown
        trigger={
          <Button
            name={<MoreHorizontal className="h-4 w-4" />}
            className="h-8 w-8 p-0 hover:bg-gray-100 rounded"
          />
        }
        isOpen={showActionsMenu === index}
        onToggle={() => setShowActionsMenu(showActionsMenu === index ? null : index)}>
        {' '}
        {actions.map((action) => (
          <DropdownItem
            key={action.key}
            onClick={() => {
              action.onClick();
              setShowActionsMenu(null);
            }}
            disabled={action.disabled}>
            {' '}
            <span className={`mr-2 ${action.variant === 'destructive' ? 'text-red-600' : ''}`}>
              {' '}
              {action.icon}{' '}
            </span>{' '}
            <span className={action.variant === 'destructive' ? 'text-red-600' : ''}>
              {' '}
              {action.label}{' '}
            </span>{' '}
          </DropdownItem>
        ))}{' '}
      </CustomDropdown>
    );
  };

  return (
    <div
      className="w-full bg-white rounded-lg border shadow-sm"
      onKeyDown={handleKeyDown}
      tabIndex={0}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-2 border-b">
        <div className="flex items-center gap-3">
          {titleIcon && <span className="text-blue-600">{titleIcon}</span>}
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          <div className="relative flex-1 sm:w-80">
            <Input
              placeholder={searchPlaceholder}
              value={search}
              onChange={handleSearchChange}
              className={'border rounded placeholder:text-[14px]'}
              leftIcon={<Search className="h-4 w-4 text-gray-400" />}
              rightIcon={
                search && (
                  <Button
                    onClick={clearSearch}
                    name={<X className="h-4 w-4" />}
                    className="h-6 w-6 p-0 hover:bg-gray-100 rounded text-gray-400 hover:text-gray-600"
                  />
                )
              }
            />
          </div>
          {onAddNew && (
            <Button
              onClick={onAddNew}
              name={buttonName}
              className="whitespace-nowrap bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md"
            />
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto h-[65vh]">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              {visibleHeaders.map((column, i) => (
                <th
                  key={i}
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {column.header}
                </th>
              ))}
              {(onView || onEdit || onDelete || onPdf || customColumn) && (
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
              {customHeader}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={visibleHeaders.length + 1} className="px-4 py-8 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-500">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : paginatedData?.length > 0 ? (
              paginatedData.map((item, i) => (
                <tr key={item.id || startIndex + i} className="hover:bg-gray-50 transition-colors">
                  {visibleHeaders.map((column, j) => (
                    <td
                      key={j}
                      className={`px-4 py-3 text-sm text-gray-700 ${column.className || ''}`}>
                      {column.render
                        ? column.render(item, startIndex + i)
                        : column.data === 'sno'
                          ? startIndex + i + 1
                          : item[column.data] !== undefined && item[column.data] !== null
                            ? String(item[column.data])
                            : '–'}
                    </td>
                  ))}
                  {(onView || onEdit || onDelete || onPdf || customColumn) && (
                    <td className="px-4 py-3 text-center">{renderActions(item, i)}</td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={visibleHeaders.length + 1}
                  className="px-4 py-8 text-center text-gray-500">
                  {search ? `No results found for "${search}"` : emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with Pagination */}
      {totalItems > 0 && totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 border-t bg-white rounded-b-lg">
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <span>Show</span>
            <PageSizeSelector />
            <span>of {totalItems} results</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            <div className="flex items-center gap-1">
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                name={<ChevronLeft className="h-4 w-4" />}
                className={`h-8 w-8 p-0 border rounded ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
              />
              {paginationRange.map((pageNumber, index) => {
                if (pageNumber === DOTS) {
                  return (
                    <span key={`${pageNumber}-${index}`} className="px-2 py-1 text-gray-500">
                      ...
                    </span>
                  );
                }
                return (
                  <Button
                    key={pageNumber}
                    onClick={() => handlePageChange(pageNumber)}
                    name={pageNumber}
                    className={`h-8 w-8 p-0 rounded ${
                      pageNumber === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'border hover:bg-gray-50'
                    }`}
                  />
                );
              })}
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                name={<ChevronRight className="h-4 w-4" />}
                className={`h-8 w-8 p-0 border rounded ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// PropTypes remain the same
Table.propTypes = {
  title: PropTypes.string,
  titleIcon: PropTypes.node,
  buttonName: PropTypes.string,
  onAddNew: PropTypes.func,
  header: PropTypes.arrayOf(
    PropTypes.shape({
      header: PropTypes.string.isRequired,
      show: PropTypes.bool.isRequired,
      data: PropTypes.string,
      className: PropTypes.string,
      render: PropTypes.func
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  searchBy: PropTypes.arrayOf(PropTypes.string),
  searchPlaceholder: PropTypes.string,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onPdf: PropTypes.func,
  customHeader: PropTypes.node,
  customColumn: PropTypes.bool,
  itemsPerPage: PropTypes.number,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string
};
