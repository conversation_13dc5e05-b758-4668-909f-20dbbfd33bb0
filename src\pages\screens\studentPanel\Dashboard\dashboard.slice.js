import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  onboardAssessment: null
};

export const studentDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getOnboardAssessmentService: builder.query({
      query: (query) => {
        return `/analytics/onboard-assessment/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    })
  })
});

const studentDashboardSlice = createSlice({
  name: 'studentDashboard',
  initialState,
  reducers: {
    setOnboardAssessment(state, action) {
      state.onboardAssessment = action.payload;
    },
    clearOnboardAssessment(state) {
      state.onboardAssessment = null;
    }
  }
});

export const { useLazyGetOnboardAssessmentServiceQuery } = studentDashboardApiSlice;
export const { setOnboardAssessment, clearOnboardAssessment } = studentDashboardSlice.actions;
export const selectStudentOverview = (state) => state.studentDashboard.onboardAssessment;
export default studentDashboardSlice.reducer;
