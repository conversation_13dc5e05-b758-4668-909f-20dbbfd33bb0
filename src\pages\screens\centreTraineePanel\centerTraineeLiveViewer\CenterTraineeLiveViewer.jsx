// import { useEffect, useState, useRef } from "react"
// import { Room, RoomEvent } from "livekit-client"
// import { motion, AnimatePresence } from "framer-motion"
// import { toast, ToastContainer } from 'react-toastify'
// import 'react-toastify/dist/ReactToastify.css'
// import {
//   useLazyGetCenterLiveViewerQuery,
//   useJoinLiveStreamMutation,
//   useStartTranslationSessionMutation,
//   useStopTranslationSessionMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
// } from "./centerTraineeLive.slice"
// import LiveQuiz from "./LiveQuiz"

// const CenterTraineeLiveViewer = () => {
//   const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery()
//   const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation()
//   const [startTranslationSession] = useStartTranslationSessionMutation()
//   const [stopTranslationSession] = useStopTranslationSessionMutation()
//   const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation()
//   const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery()

//   // Stream viewing states
//   const [isViewingStream, setIsViewingStream] = useState(false)
//   const [currentStream, setCurrentStream] = useState(null)
//   const [livekitRoom, setLivekitRoom] = useState(null)
//   const [livekitConnected, setLivekitConnected] = useState(false)
//   const [participants, setParticipants] = useState([])
//   const [remoteVideoTracks, setRemoteVideoTracks] = useState([])
//   const [remoteAudioTracks, setRemoteAudioTracks] = useState([])
//   const [connectionStatus, setConnectionStatus] = useState("")
//   const [hasScreenShare, setHasScreenShare] = useState(false)
//   const [hasCameraTrack, setHasCameraTrack] = useState(false)

//   // Refs for video elements
//   const mainVideoRef = useRef(null)
//   const screenVideoRef = useRef(null)
//   const cameraVideoRef = useRef(null)
//   const cameraMainRef = useRef(null)

//   // Translation states
//   const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
//   const [translationSession, setTranslationSession] = useState(null)
//   const [translationWebSocket, setTranslationWebSocket] = useState(null)
//   const [sourceLanguage, setSourceLanguage] = useState("en")
//   const [targetLanguage, setTargetLanguage] = useState("ta")
//   const [translationStatus, setTranslationStatus] = useState("")
//   const [currentTranscription, setCurrentTranscription] = useState("")
//   const [currentTranslation, setCurrentTranslation] = useState("")
//   const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false)
//   const [audioQueueLength, setAudioQueueLength] = useState(0)

//   // Translation audio refs
//   const audioContextRef = useRef(null)
//   const translationWebSocketRef = useRef(null)

//   // Audio Queue System refs and state
//   const audioQueueRef = useRef([])
//   const currentAudioSourceRef = useRef(null)
//   const currentHtmlAudioRef = useRef(null)
//   const isProcessingQueueRef = useRef(false)

//   // Chat states
//   const [chatMessages, setChatMessages] = useState([])
//   const [newMessage, setNewMessage] = useState("")
//   const [isChatOpen, setIsChatOpen] = useState(false)
//   const [unreadMessages, setUnreadMessages] = useState(0)
//   const [activeSidebarTab, setActiveSidebarTab] = useState("info") // 'info', 'features', 'participants', 'connection', 'translation'

//   // Quiz states
//   const [receivedQuizId, setReceivedQuizId] = useState(null)
//   const [showQuizNotification, setShowQuizNotification] = useState(false)
//   const [isQuizOpen, setIsQuizOpen] = useState(false)

//   // Available languages
//   const availableLanguages = [
//     { code: "en", name: "English", flag: "🇺🇸" },
//     { code: "ta", name: "Tamil", flag: "🇮🇳" },
//     { code: "hi", name: "Hindi", flag: "🇮🇳" },
//     { code: "te", name: "Telugu", flag: "🇮🇳" },
//     { code: "kn", name: "Kannada", flag: "🇮🇳" },
//   ]

//   // Mute/unmute original audio based on translation state
//   useEffect(() => {
//     remoteAudioTracks.forEach(({ audioElement }) => {
//       if (audioElement) {
//         audioElement.muted = isTranslationEnabled
//       }
//     })
//   }, [isTranslationEnabled, remoteAudioTracks])

//   useEffect(() => {
//     trigger()
//     const interval = setInterval(() => trigger(), 30000)
//     return () => clearInterval(interval)
//   }, [trigger])

//   useEffect(() => {
//     return () => {
//       if (livekitRoom) {
//         livekitRoom.disconnect()
//         setLivekitRoom(null)
//       }
//       cleanupTranslation()
//     }
//   }, [livekitRoom])

//   // HTTP-based chat system
//   useEffect(() => {
//     if (isViewingStream && currentStream) {
//       console.log("💬 VIEWER: Starting HTTP-based chat for session:", currentStream.session_id)
//       setChatMessages([])
//       loadChatHistory()
//       const pollInterval = setInterval(() => loadChatHistory(), 2000)
//       return () => clearInterval(pollInterval)
//     }
//   }, [isViewingStream, currentStream])

//   const loadChatHistory = async () => {
//     if (!currentStream?.session_id) return
//     try {
//       const response = await getChatHistory(currentStream.session_id).unwrap()
//       const newMessages = response.messages || []
//       console.log("Chat history loaded:", newMessages.length, "messages")

//       setChatMessages((prev) => {
//         if (newMessages.length !== prev.length) {
//           const newCount = newMessages.length - prev.length
//           console.log("New messages detected:", newCount)

//           if (newCount > 0 && !isChatOpen) {
//             setUnreadMessages((prev) => prev + newCount)
//           }

//           // Check for new quiz messages
//           const latestMessages = newMessages.slice(-newCount)
//           console.log("Checking latest messages for quiz:", latestMessages)

//           latestMessages.forEach(message => {
//             console.log("Checking message:", message.message)
//             // Check if message contains quiz start pattern
//             if (message.message && message.message.includes('QUIZ_START:')) {
//               console.log("Quiz message found:", message.message)
//               const quizMatch = message.message.match(/QUIZ_START:([a-zA-Z0-9]+)/)
//               if (quizMatch && quizMatch[1]) {
//                 const quizId = quizMatch[1]
//                 console.log("Quiz detected! Quiz ID:", quizId)
//                 setReceivedQuizId(quizId)
//                 setShowQuizNotification(true)
//                 toast.success('🎯 Quiz Started! Click "Open Quiz" to participate.', {
//                   position: "top-right",
//                   autoClose: 5000,
//                   hideProgressBar: false,
//                   closeOnClick: true,
//                   pauseOnHover: true,
//                   draggable: true,
//                 })
//               }
//             }
//           })

//           return newMessages
//         }
//         return prev
//       })
//     } catch (error) {
//       console.log("❌ VIEWER: Failed to load chat history:", error.message)
//     }
//   }

//   // Audio Queue Management
//   const addToAudioQueue = (audioData) => {
//     audioQueueRef.current.push(audioData)
//     setAudioQueueLength(audioQueueRef.current.length)
//     processAudioQueue()
//   }

//   const processAudioQueue = async () => {
//     if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) return
//     isProcessingQueueRef.current = true
//     while (audioQueueRef.current.length > 0) {
//       const audioData = audioQueueRef.current.shift()
//       setAudioQueueLength(audioQueueRef.current.length)
//       await playAudioFromQueue(audioData)
//     }
//     isProcessingQueueRef.current = false
//     setAudioQueueLength(0)
//   }

//   const stopCurrentAudio = () => {
//     if (currentAudioSourceRef.current) {
//       try {
//         currentAudioSourceRef.current.stop()
//         currentAudioSourceRef.current.disconnect()
//       } catch (error) {
//         console.warn("⚠️ Error stopping Web Audio source:", error)
//       }
//       currentAudioSourceRef.current = null
//     }
//     if (currentHtmlAudioRef.current) {
//       try {
//         currentHtmlAudioRef.current.pause()
//         currentHtmlAudioRef.current.currentTime = 0
//         if (currentHtmlAudioRef.current.src) {
//           URL.revokeObjectURL(currentHtmlAudioRef.current.src)
//         }
//       } catch (error) {
//         console.warn("⚠️ Error stopping HTML5 Audio:", error)
//       }
//       currentHtmlAudioRef.current = null
//     }
//     setIsPlayingTranslatedAudio(false)
//   }

//   const clearAudioQueue = () => {
//     audioQueueRef.current = []
//     setAudioQueueLength(0)
//     stopCurrentAudio()
//     isProcessingQueueRef.current = false
//   }

//   const cleanupTranslation = () => {
//     if (translationWebSocket) {
//       translationWebSocket.close()
//       setTranslationWebSocket(null)
//     }
//     if (translationWebSocketRef.current) {
//       translationWebSocketRef.current.close()
//       translationWebSocketRef.current = null
//     }
//     if (audioContextRef.current && audioContextRef.current.state !== "closed") {
//       audioContextRef.current.close()
//       audioContextRef.current = null
//     }
//     clearAudioQueue()
//     setTranslationSession(null)
//     setIsTranslationEnabled(false)
//     setTranslationStatus("")
//     setCurrentTranscription("")
//     setCurrentTranslation("")
//   }

//   useEffect(() => {
//     if (remoteVideoTracks.length === 0) return
//     remoteVideoTracks.forEach(({ track, publication }) => {
//       if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//         if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
//           track.attach(screenVideoRef.current)
//         }
//       } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
//         if (hasScreenShare) {
//           if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
//             track.attach(cameraVideoRef.current)
//           }
//         } else {
//           if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
//             track.attach(cameraMainRef.current)
//           }
//         }
//       }
//     })
//   }, [remoteVideoTracks, hasScreenShare, hasCameraTrack])

//   const connectToLiveKitRoom = async (token, url) => {
//     try {
//       setConnectionStatus("Connecting...")
//       const room = new Room()
//       room.on(RoomEvent.Connected, () => {
//         setLivekitConnected(true)
//         setConnectionStatus("Connected")
//       })
//       room.on(RoomEvent.Disconnected, () => {
//         setLivekitConnected(false)
//         setConnectionStatus("Disconnected")
//         setIsViewingStream(false)
//       })
//       room.on(RoomEvent.ParticipantConnected, (participant) => {
//         setParticipants((prev) => [...prev, participant])
//       })
//       room.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
//       })
//       room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = [...prev, { track, participant, publication }]
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })
//           if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//             if (screenVideoRef.current) track.attach(screenVideoRef.current)
//           } else if (
//             publication.source === "camera" ||
//             publication.trackName === "teacher_camera" ||
//             !publication.source
//           ) {
//             if (cameraVideoRef.current) track.attach(cameraVideoRef.current)
//             if (
//               !remoteVideoTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ) &&
//               cameraMainRef.current
//             ) {
//               track.attach(cameraMainRef.current)
//             }
//           }
//         } else if (track.kind === "audio") {
//           const audioElement = track.attach()
//           setRemoteAudioTracks((prev) => [...prev, { track, participant, publication, audioElement }])
//         }
//       })
//       room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = prev.filter((t) => t.track !== track)
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })
//           track.detach()
//         } else if (track.kind === "audio") {
//           setRemoteAudioTracks((prev) => {
//             const trackInfo = prev.find((t) => t.track === track)
//             if (trackInfo && trackInfo.audioElement) {
//               track.detach(trackInfo.audioElement)
//             }
//             return prev.filter((t) => t.track !== track)
//           })
//         }
//       })
//       await room.connect(url, token)
//       setLivekitRoom(room)
//     } catch (err) {
//       setConnectionStatus("Connection failed: " + err.message)
//       alert("Failed to connect to stream: " + err.message)
//     }
//   }

//   const handleJoinStream = async (stream) => {
//     try {
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) {
//         alert("Please login first to join the stream.")
//         return
//       }
//       if (!stream.session_id || !stream.teacher_id) {
//         alert("Invalid stream data. Please try again.")
//         return
//       }
//       const sessionData = { session_id: stream.session_id, teacher_id: stream.teacher_id }
//       const response = await joinStream(sessionData).unwrap()
//       if (response.token && response.livekit_url) {
//         const correctSessionId = response.stream_info?.session_id || response.room_name || stream.session_id
//         const updatedStream = { ...stream, session_id: correctSessionId }
//         setCurrentStream(updatedStream)
//         setIsViewingStream(true)
//         await connectToLiveKitRoom(response.token, response.livekit_url)
//       } else {
//         alert("Invalid stream response. Missing connection credentials.")
//       }
//     } catch (error) {
//       alert("Failed to join stream: " + (error.data?.message || "Unknown error"))
//     }
//   }

//   const handleLeaveStream = () => {
//     if (livekitRoom) {
//       livekitRoom.disconnect()
//       setLivekitRoom(null)
//     }
//     cleanupTranslation()
//     setIsViewingStream(false)
//     setCurrentStream(null)
//     setLivekitConnected(false)
//     setParticipants([])
//     setRemoteVideoTracks([])
//     setRemoteAudioTracks([])
//     setConnectionStatus("")
//     setHasScreenShare(false)
//     setHasCameraTrack(false)
//   }

//   const startTranslation = async () => {
//     try {
//       setTranslationStatus("Starting translation...")
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) throw new Error("User not authenticated")
//       const data = await startTranslationSession({
//         user_id: userId,
//         stream_session_id: currentStream.session_id,
//         source_language: sourceLanguage,
//         target_language: targetLanguage,
//       }).unwrap()
//       setTranslationSession(data.session_id)
//       await connectTranslationWebSocket(data.websocket_url)
//       await new Promise((resolve) => setTimeout(resolve, 2000))
//       const ws = translationWebSocketRef.current || translationWebSocket
//       if (!ws || ws.readyState !== WebSocket.OPEN) throw new Error("WebSocket not ready")

//       setIsTranslationEnabled(true)
//       setTranslationStatus("Translation active - receiving from teacher")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//     }
//   }

//   const stopTranslation = async () => {
//     try {
//       setTranslationStatus("Stopping translation...")
//       if (translationSession) {
//         await stopTranslationSession({ session_id: translationSession }).unwrap()
//       }
//       cleanupTranslation()
//       setTranslationStatus("Translation stopped")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//     }
//   }

//   const connectTranslationWebSocket = async (websocketUrl) => {
//     return new Promise((resolve, reject) => {
//       // Fix WebSocket URL - remove port 8012 and use correct domain
//       let correctedUrl = websocketUrl
//       if (websocketUrl.includes(':8012')) {
//         correctedUrl = websocketUrl.replace(':8012', '')
//       }
//       // Ensure it uses the correct domain format
//       if (correctedUrl.includes('sasthra.in:8012')) {
//         correctedUrl = correctedUrl.replace('sasthra.in:8012', 'sasthra.in')
//       }

//       console.log('Original WebSocket URL:', websocketUrl)
//       console.log('Corrected WebSocket URL:', correctedUrl)

//       const ws = new WebSocket(correctedUrl)
//       ws.onopen = () => {
//         setTranslationWebSocket(ws)
//         translationWebSocketRef.current = ws
//         setTranslationStatus("WebSocket connected - ready for audio")
//         resolve()
//       }
//       ws.onmessage = (event) => {
//         const data = JSON.parse(event.data)
//         handleTranslationMessage(data)
//       }
//       ws.onerror = (error) => {
//         setTranslationStatus(`WebSocket error: ${error.message || "Connection failed"}`)
//         reject(error)
//       }
//       ws.onclose = () => {
//         setTranslationWebSocket(null)
//         setTranslationStatus("WebSocket disconnected")
//       }
//     })
//   }

//   const handleTranslationMessage = (data) => {
//     switch (data.type) {
//       case "connection_established":
//         setTranslationStatus("Connected - waiting for teacher's speech")
//         break
//       case "transcription":
//         setCurrentTranscription(data.text)
//         setTranslationStatus("Receiving transcription from teacher...")
//         break
//       case "translation":
//         setCurrentTranslation(data.translated_text)
//         setTranslationStatus("Translation received...")
//         break
//       case "translated_audio":
//         setTranslationStatus("Playing translated audio...")
//         addToAudioQueue(data.audio_data)
//         break
//       case "error":
//         setTranslationStatus(`Error: ${data.message}`)
//         break
//       default:
//         break
//     }
//   }





//   const playAudioFromQueue = async (audioDataBase64) => {
//     return new Promise(async (resolve) => {
//       try {
//         setTranslationStatus("Playing audio...")
//         if (!audioDataBase64) {
//           setIsPlayingTranslatedAudio(false)
//           resolve()
//           return
//         }
//         if (!audioContextRef.current) {
//           audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
//         }
//         if (audioContextRef.current.state === "suspended") {
//           await audioContextRef.current.resume()
//         }
//         try {
//           const binaryString = atob(audioDataBase64)
//           const audioData = new Uint8Array(binaryString.length)
//           for (let i = 0; i < binaryString.length; i++) {
//             audioData[i] = binaryString.charCodeAt(i)
//           }
//           const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer)
//           const source = audioContextRef.current.createBufferSource()
//           source.buffer = audioBuffer
//           source.connect(audioContextRef.current.destination)
//           currentAudioSourceRef.current = source
//           setIsPlayingTranslatedAudio(true)
//           source.onended = () => {
//             setIsPlayingTranslatedAudio(false)
//             currentAudioSourceRef.current = null
//             setTranslationStatus("Translation active - waiting for teacher's speech")
//             resolve()
//           }
//           source.start()
//         } catch (webAudioError) {
//           try {
//             const audioBlob = new Blob(
//               [
//                 new Uint8Array(
//                   atob(audioDataBase64)
//                     .split("")
//                     .map((c) => c.charCodeAt(0)),
//                 ),
//               ],
//               { type: "audio/wav" },
//             )
//             const audioUrl = URL.createObjectURL(audioBlob)
//             const audio = new Audio(audioUrl)
//             currentHtmlAudioRef.current = audio
//             setIsPlayingTranslatedAudio(true)
//             audio.onended = () => {
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Translation active - waiting for teacher's speech")
//               resolve()
//             }
//             audio.onerror = () => {
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Audio playback error")
//               resolve()
//             }
//             await audio.play()
//           } catch (htmlAudioError) {
//             setIsPlayingTranslatedAudio(false)
//             setTranslationStatus(`Audio playback error: ${htmlAudioError.message}`)
//             resolve()
//           }
//         }
//       } catch (error) {
//         setIsPlayingTranslatedAudio(false)
//         setTranslationStatus(`Audio playback error: ${error.message}`)
//         resolve()
//       }
//     })
//   }

//   const handleSendChatMessage = async () => {
//     if (!newMessage.trim() || !currentStream) return
//     const messageData = {
//       session_id: currentStream.session_id,
//       message: newMessage.trim(),
//       sender_id: sessionStorage.getItem("userId"),
//       sender_name: sessionStorage.getItem("name") || "Viewer",
//     }
//     try {
//       await sendChatMessage(messageData).unwrap()
//       setNewMessage("")
//       setTimeout(loadChatHistory, 500)
//     } catch (error) {
//       console.error("❌ VIEWER: Error sending message:", error.message)
//     }
//   }

//   const handleKeyDown = (e) => {
//     if (e.key === "Enter" && !e.shiftKey) {
//       e.preventDefault()
//       handleSendChatMessage()
//     }
//   }

//   const toggleChat = () => {
//     setIsChatOpen(!isChatOpen)
//     if (!isChatOpen) {
//       setUnreadMessages(0)
//     }
//   }

//   const formatMessageTime = (timestamp) => {
//     return new Date(timestamp).toLocaleTimeString([], {
//       hour: "2-digit",
//       minute: "2-digit",
//     })
//   }

//   const getRoleColor = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "text-emerald-500"
//       case "student":
//         return "text-blue-500"
//       case "center_counselor":
//         return "text-purple-500"
//       default:
//         return "text-gray-500"
//     }
//   }

//   // Quiz functions
//   const handleOpenQuiz = () => {
//     if (receivedQuizId) {
//       setIsQuizOpen(true)
//       setShowQuizNotification(false)
//     }
//   }

//   const handleCloseQuiz = () => {
//     setIsQuizOpen(false)
//   }

//   const getRoleBadge = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "Teacher"
//       case "student":
//         return "Student"
//       case "center_counselor":
//         return "Counselor"
//       default:
//         return "User"
//     }
//   }

//   const formatUptime = (uptime) => {
//     const hours = Math.floor(uptime / 3600)
//     const minutes = Math.floor((uptime % 3600) / 60)
//     const seconds = Math.floor(uptime % 60)
//     if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`
//     if (minutes > 0) return `${minutes}m ${seconds}s`
//     return `${seconds}s`
//   }

//   const formatDateTime = (dateString) => new Date(dateString).toLocaleString()

//   if (isLoading)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
//       >
//         <div className="text-center">
//           <motion.div
//             animate={{ rotate: 360 }}
//             transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
//             className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto"
//           ></motion.div>
//           <motion.p
//             initial={{ y: 20, opacity: 0 }}
//             animate={{ y: 0, opacity: 1 }}
//             transition={{ delay: 0.2 }}
//             className="text-white text-lg font-semibold mt-6"
//           >
//             Loading Live Streams...
//           </motion.p>
//         </div>
//       </motion.div>
//     )

//   if (error)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-red-100"
//       >
//         <motion.div
//           initial={{ scale: 0.8, opacity: 0 }}
//           animate={{ scale: 1, opacity: 1 }}
//           transition={{ duration: 0.4 }}
//           className="bg-white rounded-2xl shadow-2xl p-8 max-w-md mx-4 border border-red-100"
//         >
//           <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
//             <svg className="h-8 w-8 text-red-600" viewBox="0 0 20 20" fill="currentColor">
//               <path
//                 fillRule="evenodd"
//                 d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
//                 clipRule="evenodd"
//               />
//             </svg>
//           </div>
//           <h3 className="text-xl font-bold text-red-800 text-center mb-2">Connection Error</h3>
//           <p className="text-red-600 text-center mb-4">{error.message || "Failed to load active streams"}</p>
//           <motion.button
//             whileHover={{ scale: 1.05 }}
//             whileTap={{ scale: 0.95 }}
//             onClick={() => trigger()}
//             className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
//           >
//             Try Again
//           </motion.button>
//         </motion.div>
//       </motion.div>
//     )

//   const activeStreams = data?.active_streams || []

//   if (isViewingStream && currentStream)
//     return (
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ duration: 0.5 }}
//         className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
//       >
//         {/* Header */}
//         <motion.div
//           initial={{ y: -50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ duration: 0.5 }}
//           className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
//         >
//           <div className="flex items-center justify-between max-w-7xl mx-auto">
//             <div className="flex items-center space-x-4">
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={handleLeaveStream}
//                 className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2"
//               >
//                 <svg
//                   className="w-5 h-5"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
//                 </svg>
//                 <span>Leave Stream</span>
//               </motion.button>
//               <div className="border-l border-gray-600 pl-4">
//                 <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
//                   Live Stream
//                 </h1>
//                 <p className="text-sm text-gray-300">Session: {currentStream.session_id}</p>
//               </div>
//             </div>
//             <div className="flex items-center space-x-6">
//               <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
//                 <motion.div
//                   animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                   transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                   className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                 ></motion.div>
//                 <span className="text-sm font-medium">{connectionStatus || "Connecting..."}</span>
//               </div>
//               <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
//                 <svg className="w-4 h-4 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
//                   />
//                 </svg>
//                 <span className="text-sm font-medium">{participants.length + 1} Participants</span>
//               </div>

//               {/* Debug Quiz ID Display */}
//               {process.env.NODE_ENV === 'development' && (
//                 <div className="bg-yellow-500 text-black px-2 py-1 rounded text-xs">
//                   Quiz ID: {receivedQuizId || 'None'}
//                 </div>
//               )}

//               {/* Quiz Notification and Open Quiz Button */}
//               {receivedQuizId && (
//                 <motion.button
//                   initial={{ scale: 0 }}
//                   animate={{ scale: 1 }}
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={handleOpenQuiz}
//                   className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold shadow-md flex items-center space-x-2 animate-pulse"
//                 >
//                   <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
//                   </svg>
//                   <span>Open Quiz (ID: {receivedQuizId})</span>
//                 </motion.button>
//               )}

//               {/* Always show manual quiz button for testing */}
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={() => setIsQuizOpen(true)}
//                 className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-semibold shadow-md flex items-center space-x-2"
//               >
//                 <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
//                 </svg>
//                 <span>Manual Quiz</span>
//               </motion.button>
//             </div>
//           </div>
//         </motion.div>

//         <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
//           {/* Video Area */}
//           <motion.div
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
//           >
//             <div className="w-full h-full flex items-center justify-center relative">
//               <video
//                 ref={screenVideoRef}
//                 autoPlay
//                 playsInline
//                 className="w-full h-full object-contain"
//               />
//               {!hasScreenShare && hasCameraTrack && (
//                 <video
//                   ref={cameraMainRef}
//                   autoPlay
//                   playsInline
//                   className="w-full h-full object-contain absolute inset-0"
//                 />
//               )}
//               {remoteVideoTracks.length === 0 && (
//                 <motion.div
//                   initial={{ opacity: 0 }}
//                   animate={{ opacity: 1 }}
//                   transition={{ duration: 0.5 }}
//                   className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/70 to-black/70"
//                 >
//                   <div className="text-center">
//                     <motion.div
//                       animate={{ rotate: 360 }}
//                       transition={{ repeat: Infinity, duration: 2, ease: "linear" }}
//                       className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto mb-4"
//                     ></motion.div>
//                     <motion.p
//                       initial={{ y: 20, opacity: 0 }}
//                       animate={{ y: 0, opacity: 1 }}
//                       transition={{ delay: 0.2 }}
//                       className="text-xl font-semibold"
//                     >
//                       Waiting for stream...
//                     </motion.p>
//                     <p className="text-gray-300 flex items-center justify-center mt-2">
//                       <motion.span
//                         animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                         transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                         className={`w-2 h-2 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                       ></motion.span>
//                       {livekitConnected ? "Connected to room" : "Connecting..."}
//                     </p>
//                   </div>
//                 </motion.div>
//               )}
//             </div>
//             {hasScreenShare && hasCameraTrack && (
//               <motion.div
//                 initial={{ scale: 0.8, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 transition={{ duration: 0.4 }}
//                 className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
//               >
//                 <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                 <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
//                   Teacher Camera
//                 </div>
//               </motion.div>
//             )}
//             {!isChatOpen && (
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={toggleChat}
//                 className="absolute bottom-6 left-6 bg-indigo-500 hover:bg-indigo-600 text-white p-3 rounded-full shadow-lg"
//               >
//                 <svg
//                   className="w-6 h-6"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                   />
//                 </svg>
//                 {unreadMessages > 0 && (
//                   <motion.span
//                     initial={{ scale: 0 }}
//                     animate={{ scale: 1 }}
//                     className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
//                   >
//                     {unreadMessages}
//                   </motion.span>
//                 )}
//               </motion.button>
//             )}
//           </motion.div>

//           {/* Chat Overlay */}
//           <AnimatePresence>
//             {isChatOpen && (
//               <motion.div
//                 initial={{ x: '100%' }}
//                 animate={{ x: 0 }}
//                 exit={{ x: '100%' }}
//                 transition={{ type: 'spring', stiffness: 300, damping: 30 }}
//                 className="absolute right-0 top-0 bottom-0 w-96 bg-gray-800 text-white shadow-lg border-l border-gray-700 z-10"
//               >
//                 <div className="p-4 border-b border-gray-700 flex justify-between items-center">
//                   <h3 className="text-lg font-semibold">Live Chat</h3>
//                   <button onClick={toggleChat} className="text-gray-400 hover:text-white">
//                     <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                     </svg>
//                   </button>
//                 </div>
//                 <div className="h-[calc(100%-120px)] overflow-y-auto p-4">
//                   {isLoadingHistory ? (
//                     <div className="flex items-center justify-center h-full">
//                       <motion.div
//                         animate={{ rotate: 360 }}
//                         transition={{ repeat: Infinity, duration: 1 }}
//                         className="w-6 h-6 border-2 border-indigo-200 border-t-indigo-500 rounded-full"
//                       ></motion.div>
//                     </div>
//                   ) : chatMessages.length === 0 ? (
//                     <div className="flex items-center justify-center h-full text-gray-400">
//                       No messages yet
//                     </div>
//                   ) : (
//                     <AnimatePresence>
//                       {chatMessages.map((message, index) => (
//                         <motion.div
//                           key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
//                           initial={{ opacity: 0, y: 10 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           exit={{ opacity: 0, y: -10 }}
//                           className={`p-2 hover:bg-gray-700/30 rounded mb-2 ${
//                             message.message && message.message.includes('QUIZ_START:')
//                               ? 'bg-purple-900/50 border border-purple-500'
//                               : ''
//                           }`}
//                         >
//                           <div className="flex items-start justify-between">
//                             <div className="flex-1">
//                               <div className="flex items-center space-x-2 mb-1">
//                                 <span className={`text-sm font-medium ${getRoleColor(message.sender_role)}`}>
//                                   {message.sender_name}
//                                 </span>
//                                 <span className="text-xs bg-gray-700 px-2 py-1 rounded">
//                                   {getRoleBadge(message.sender_role)}
//                                 </span>
//                                 {message.message && message.message.includes('QUIZ_START:') && (
//                                   <span className="text-xs bg-purple-500 text-white px-2 py-1 rounded">
//                                     QUIZ
//                                   </span>
//                                 )}
//                               </div>
//                               <p className="text-sm text-gray-200">{message.message}</p>
//                             </div>
//                             <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
//                           </div>
//                         </motion.div>
//                       ))}
//                     </AnimatePresence>
//                   )}
//                 </div>
//                 <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-800 border-t border-gray-700">
//                   <div className="flex space-x-2">
//                     <input
//                       type="text"
//                       value={newMessage}
//                       onChange={(e) => setNewMessage(e.target.value)}
//                       onKeyDown={handleKeyDown}
//                       placeholder="Type a message..."
//                       className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
//                     />
//                     <motion.button
//                       whileHover={{ scale: 1.05 }}
//                       whileTap={{ scale: 0.95 }}
//                       onClick={handleSendChatMessage}
//                       disabled={!newMessage.trim() || isSendingMessage}
//                       className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
//                     >
//                       {isSendingMessage ? (
//                         <motion.div
//                           animate={{ rotate: 360 }}
//                           transition={{ repeat: Infinity, duration: 1 }}
//                           className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
//                         ></motion.div>
//                       ) : (
//                         "Send"
//                       )}
//                     </motion.button>
//                   </div>
//                 </div>
//               </motion.div>
//             )}
//           </AnimatePresence>

//           {/* Sidebar */}
//           <motion.div
//             initial={{ x: 100, opacity: 0 }}
//             animate={{ x: 0, opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="w-20 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
//           >
//             <div className="flex flex-col items-center py-4 space-y-4">
//               {/* Stream Information */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('info')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'info' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
//                 </svg>
//               </motion.button>

//               {/* Features */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('features')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'features' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
//                 </svg>
//               </motion.button>

//               {/* Participants */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('participants')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'participants' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
//                 </svg>
//               </motion.button>

//               {/* Connection Status */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('connection')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'connection' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
//                 </svg>
//               </motion.button>

//               {/* Translation */}
//               <motion.button
//                 whileHover={{ scale: 1.1 }}
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => setActiveSidebarTab('translation')}
//                 className={`p-3 rounded-lg ${activeSidebarTab === 'translation' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
//               >
//                 <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
//                 </svg>
//               </motion.button>
//             </div>
//           </motion.div>

//           {/* Sidebar Content */}
//           <motion.div
//             initial={{ x: 100, opacity: 0 }}
//             animate={{ x: 0, opacity: 1 }}
//             transition={{ duration: 0.5 }}
//             className="w-80 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
//           >
//             <div className="p-6 space-y-6">
//               {/* Stream Information */}
//               {activeSidebarTab === 'info' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.1 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Stream Information</h3>
//                   <div className="space-y-2 text-sm">
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Teacher ID:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.teacher_id}</span>
//                     </div>
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Quality:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.quality || "Standard"}</span>
//                     </div>
//                     <div className="flex justify-between bg-gray-700/50 p-2 rounded">
//                       <span className="text-gray-300">Viewers:</span>
//                       <span className="font-medium text-indigo-400">{currentStream.viewer_count || 0}</span>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Features */}
//               {activeSidebarTab === 'features' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.2 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Features</h3>
//                   <div className="space-y-2">
//                     {currentStream.features?.screen_sharing && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Screen Sharing Active</span>
//                       </div>
//                     )}
//                     {currentStream.features?.chat_enabled && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Chat Available</span>
//                       </div>
//                     )}
//                     {currentStream.features?.recording_enabled && (
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <span className="text-indigo-400">Recording Active</span>
//                       </div>
//                     )}
//                   </div>
//                 </motion.div>
//               )}

//               {/* Participants */}
//               {activeSidebarTab === 'participants' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.3 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Participants ({participants.length + 1})</h3>
//                   <div className="space-y-2 max-h-60 overflow-y-auto">
//                     <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                       <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
//                         T
//                       </div>
//                       <div className="flex-1">
//                         <div className="font-medium text-indigo-400">Teacher</div>
//                         <div className="text-xs text-gray-400">Host</div>
//                       </div>
//                     </div>
//                     <AnimatePresence>
//                       {participants.map((participant, index) => (
//                         <motion.div
//                           key={participant.identity || index}
//                           initial={{ opacity: 0, y: 10 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           exit={{ opacity: 0, y: -10 }}
//                           className="flex items-center bg-indigo-500/10 p-2 rounded"
//                         >
//                           <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
//                             {participant.identity?.charAt(0)?.toUpperCase() || "U"}
//                           </div>
//                           <div className="flex-1">
//                             <div className="font-medium text-indigo-400">{participant.identity || "Unknown"}</div>
//                             <div className="text-xs text-gray-400">Viewer</div>
//                           </div>
//                         </motion.div>
//                       ))}
//                     </AnimatePresence>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Connection Status */}
//               {activeSidebarTab === 'connection' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.4 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
//                   <div className="space-y-2">
//                     <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                       <motion.div
//                         animate={{ scale: livekitConnected ? [1, 1.2, 1] : 1 }}
//                         transition={{ repeat: livekitConnected ? Infinity : 0, duration: 1.5 }}
//                         className={`w-3 h-3 rounded-full mr-2 ${livekitConnected ? "bg-green-400" : "bg-red-400"}`}
//                       ></motion.div>
//                       <span className="font-medium">{livekitConnected ? "Connected" : "Disconnected"}</span>
//                     </div>
//                     <div className="grid grid-cols-2 gap-2 text-sm">
//                       <div className="bg-gray-700/50 p-2 rounded text-center">
//                         <div className="text-gray-400">Video Tracks</div>
//                         <div className="font-bold text-indigo-400">{remoteVideoTracks.length}</div>
//                       </div>
//                       <div className="bg-gray-700/50 p-2 rounded text-center">
//                         <div className="text-gray-400">Audio Tracks</div>
//                         <div className="font-bold text-indigo-400">{remoteAudioTracks.length}</div>
//                       </div>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}

//               {/* Translation */}
//               {activeSidebarTab === 'translation' && (
//                 <motion.div
//                   initial={{ y: 20, opacity: 0 }}
//                   animate={{ y: 0, opacity: 1 }}
//                   transition={{ delay: 0.5 }}
//                   className="bg-gray-900 rounded-lg p-4 shadow"
//                 >
//                   <h3 className="text-lg font-semibold mb-3">Live Translation</h3>
//                   <div className="space-y-4">
//                     <div className="flex items-center justify-between">
//                       <span className="text-gray-300 font-medium">Enable Translation</span>
//                       <motion.button
//                         whileHover={{ scale: 1.05 }}
//                         whileTap={{ scale: 0.95 }}
//                         onClick={isTranslationEnabled ? stopTranslation : startTranslation}
//                         className={`px-4 py-2 rounded-lg font-semibold ${
//                           isTranslationEnabled
//                             ? "bg-red-500 hover:bg-red-600 text-white"
//                             : "bg-indigo-500 hover:bg-indigo-600 text-white"
//                         }`}
//                       >
//                         {isTranslationEnabled ? "Stop" : "Start"}
//                       </motion.button>
//                     </div>
//                     <div className="grid grid-cols-2 gap-3">
//                       <div>
//                         <label className="block text-xs text-gray-400 mb-1">From</label>
//                         <select
//                           value={sourceLanguage}
//                           onChange={(e) => setSourceLanguage(e.target.value)}
//                           disabled={isTranslationEnabled}
//                           className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
//                         >
//                           {availableLanguages.map((lang) => (
//                             <option key={lang.code} value={lang.code}>
//                               {lang.flag} {lang.name}
//                             </option>
//                           ))}
//                         </select>
//                       </div>
//                       <div>
//                         <label className="block text-xs text-gray-400 mb-1">To</label>
//                         <select
//                           value={targetLanguage}
//                           onChange={(e) => setTargetLanguage(e.target.value)}
//                           disabled={isTranslationEnabled}
//                           className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
//                         >
//                           {availableLanguages.map((lang) => (
//                             <option key={lang.code} value={lang.code}>
//                               {lang.flag} {lang.name}
//                             </option>
//                           ))}
//                         </select>
//                       </div>
//                     </div>
//                     <div className="space-y-2">
//                       <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                         <motion.div
//                           animate={{ scale: isTranslationEnabled ? [1, 1.2, 1] : 1 }}
//                           transition={{ repeat: isTranslationEnabled ? Infinity : 0, duration: 1.5 }}
//                           className={`w-3 h-3 rounded-full mr-2 ${isTranslationEnabled ? "bg-green-400" : "bg-gray-400"}`}
//                         ></motion.div>
//                         <span className="text-sm">
//                           {translationStatus || (isTranslationEnabled ? "Translation Active" : "Translation Inactive")}
//                         </span>
//                       </div>
//                       {isPlayingTranslatedAudio && (
//                         <div className="flex items-center bg-indigo-500/10 p-2 rounded">
//                           <motion.div
//                             animate={{ scale: [1, 1.2, 1] }}
//                             transition={{ repeat: Infinity, duration: 1 }}
//                             className="w-3 h-3 bg-indigo-400 rounded-full mr-2"
//                           ></motion.div>
//                           <span className="text-sm">Playing Translated Audio</span>
//                         </div>
//                       )}
//                       {audioQueueLength > 0 && (
//                         <div className="flex items-center bg-yellow-500/10 p-2 rounded">
//                           <motion.div
//                             animate={{ scale: [1, 1.2, 1] }}
//                             transition={{ repeat: Infinity, duration: 1 }}
//                             className="w-3 h-3 bg-yellow-400 rounded-full mr-2"
//                           ></motion.div>
//                           <span className="text-sm">Audio Queue: {audioQueueLength} pending</span>
//                         </div>
//                       )}
//                     </div>
//                     {isTranslationEnabled && (
//                       <div className="bg-gray-700/50 rounded-lg p-2 text-xs">
//                         <div className="flex justify-between">
//                           <span className="text-gray-400">WebSocket:</span>
//                           <span
//                             className={
//                               translationWebSocket
//                                 ? translationWebSocket.readyState === 1
//                                   ? "text-green-400"
//                                   : translationWebSocket.readyState === 0
//                                     ? "text-yellow-400"
//                                     : "text-red-400"
//                                 : "text-red-400"
//                             }
//                           >
//                             {translationWebSocket
//                               ? translationWebSocket.readyState === 1
//                                 ? "Connected"
//                                 : translationWebSocket.readyState === 0
//                                   ? "Connecting"
//                                   : "Closed"
//                               : "Not Created"}
//                           </span>
//                         </div>
//                       </div>
//                     )}
//                     {currentTranscription && (
//                       <motion.div
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         className="bg-gray-700/50 rounded-lg p-3"
//                       >
//                         <div className="text-xs text-indigo-400 mb-1">Original ({sourceLanguage.toUpperCase()}):</div>
//                         <div className="text-sm">{currentTranscription}</div>
//                       </motion.div>
//                     )}
//                     {currentTranslation && (
//                       <motion.div
//                         initial={{ opacity: 0 }}
//                         animate={{ opacity: 1 }}
//                         className="bg-indigo-900/30 rounded-lg p-3"
//                       >
//                         <div className="text-xs text-indigo-400 mb-1">Translation ({targetLanguage.toUpperCase()}):</div>
//                         <div className="text-sm">{currentTranslation}</div>
//                       </motion.div>
//                     )}
//                     <div className="flex items-center bg-blue-500/10 p-2 rounded text-xs">
//                       <span>Translation receives audio from teacher's microphone</span>
//                     </div>
//                   </div>
//                 </motion.div>
//               )}
//             </div>
//           </motion.div>
//         </div>

//         {/* Quiz Overlay */}
//         <AnimatePresence>
//           {isQuizOpen && receivedQuizId && (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               exit={{ opacity: 0 }}
//               className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
//             >
//               <motion.div
//                 initial={{ scale: 0.9, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 exit={{ scale: 0.9, opacity: 0 }}
//                 className="w-full h-full max-w-7xl mx-4"
//               >
//                 <LiveQuiz quizId={receivedQuizId} onClose={handleCloseQuiz} />
//               </motion.div>
//             </motion.div>
//           )}
//         </AnimatePresence>
//       </motion.div>
//     )

//   return (
//     <motion.div
//       initial={{ opacity: 0 }}
//       animate={{ opacity: 1 }}
//       transition={{ duration: 0.5 }}
//       className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-100"
//     >
//       <div className="max-w-7xl mx-auto px-4 py-8">
//         <motion.div
//           initial={{ y: -50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ duration: 0.5 }}
//           className="text-center mb-12"
//         >
//           <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
//             Live Streams
//           </h1>
//           <p className="text-lg text-gray-600 mt-2">Join active streaming sessions</p>
//           <motion.button
//             whileHover={{ scale: 1.05 }}
//             whileTap={{ scale: 0.95 }}
//             onClick={() => trigger()}
//             className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
//           >
//             Refresh Streams
//           </motion.button>
//         </motion.div>

//         {activeStreams.length === 0 ? (
//           <motion.div
//             initial={{ scale: 0.8, opacity: 0 }}
//             animate={{ scale: 1, opacity: 1 }}
//             transition={{ duration: 0.4 }}
//             className="text-center py-20"
//           >
//             <div className="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
//               <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={1}
//                   d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                 />
//               </svg>
//               <h3 className="text-xl font-semibold text-gray-900 mb-2">No Active Streams</h3>
//               <p className="text-gray-500">There are currently no live streaming sessions available.</p>
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={() => trigger()}
//                 className="mt-4 bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg font-semibold"
//               >
//                 Check Again
//               </motion.button>
//             </div>
//           </motion.div>
//         ) : (
//           <motion.div
//             className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             transition={{ staggerChildren: 0.2 }}
//           >
//             {activeStreams.map((stream, index) => (
//               <motion.div
//                 key={stream.session_id || index}
//                 initial={{ y: 20, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ delay: index * 0.1 }}
//                 className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
//               >
//                 <div className="flex items-center justify-between mb-4">
//                   <span className="text-sm font-semibold text-indigo-600">Session: {stream.session_id}</span>
//                   <span className="bg-indigo-100 text-indigo-600 px-2 py-1 rounded text-xs">
//                     {stream.quality || "Standard"}
//                   </span>
//                 </div>
//                 <p className="text-gray-600 text-sm mb-4">Teacher ID: {stream.teacher_id}</p>
//                 <div className="grid grid-cols-2 gap-4 mb-4">
//                   <div className="text-center bg-indigo-50 p-3 rounded">
//                     <div className="text-lg font-bold text-indigo-600">{stream.viewer_count || 0}</div>
//                     <div className="text-xs text-gray-500">Viewers</div>
//                   </div>
//                   <div className="text-center bg-indigo-50 p-3 rounded">
//                     <div className="text-lg font-bold text-indigo-600">{formatUptime(stream.uptime || 0)}</div>
//                     <div className="text-xs text-gray-500">Uptime</div>
//                   </div>
//                 </div>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   onClick={() => handleJoinStream(stream)}
//                   disabled={isJoining}
//                   className="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg font-semibold"
//                 >
//                   {isJoining ? "Joining..." : "Join Stream"}
//                 </motion.button>
//               </motion.div>
//             ))}
//           </motion.div>
//         )}
//       </div>

//       {/* Toast Notifications */}
//       <ToastContainer
//         position="top-right"
//         autoClose={5000}
//         hideProgressBar={false}
//         newestOnTop={false}
//         closeOnClick
//         rtl={false}
//         pauseOnFocusLoss
//         draggable
//         pauseOnHover
//         theme="dark"
//       />
//     </motion.div>
//   )
// }

// export default CenterTraineeLiveViewer


//-===========22-07-2025 added recived audio api SSE-Events
import { useEffect, useState, useRef } from 'react';
import { Room, RoomEvent, Track } from 'livekit-client';
import { motion, AnimatePresence } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  useLazyGetCenterLiveViewerQuery,
  useJoinLiveStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
} from './centerTraineeLive.slice';
import LiveQuiz from './LiveQuiz';

const CenterTraineeLiveViewer = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery();
  const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation();
  const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation();
  const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery();

  // Stream viewing states
  const [isViewingStream, setIsViewingStream] = useState(false);
  const [currentStream, setCurrentStream] = useState(null);
  const [livekitRoom, setLivekitRoom] = useState(null);
  const [livekitConnected, setLivekitConnected] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [remoteVideoTracks, setRemoteVideoTracks] = useState([]);
  const [remoteAudioTracks, setRemoteAudioTracks] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('');
  const [hasScreenShare, setHasScreenShare] = useState(false);
  const [hasCameraTrack, setHasCameraTrack] = useState(false);

  // Refs for video elements
  const mainVideoRef = useRef(null);
  const screenVideoRef = useRef(null);
  const cameraVideoRef = useRef(null);
  const cameraMainRef = useRef(null);

  // Translation states
  const [isReceivingTranslation, setIsReceivingTranslation] = useState(false);
  const [translationEventSource, setTranslationEventSource] = useState(null);
  const [translationSessionId, setTranslationSessionId] = useState('');
  const [inputSessionId, setInputSessionId] = useState('');
  const [targetLanguage, setTargetLanguage] = useState('ta');
  const [translationStatus, setTranslationStatus] = useState('');
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [currentTranslation, setCurrentTranslation] = useState('');
  const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false);
  const [audioQueueLength, setAudioQueueLength] = useState(0);

  // Translation audio refs
  const audioContextRef = useRef(null);
  const audioQueueRef = useRef([]);
  const currentAudioSourceRef = useRef(null);
  const currentHtmlAudioRef = useRef(null);
  const isProcessingQueueRef = useRef(false);

  // Chat states
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [activeSidebarTab, setActiveSidebarTab] = useState('info');

  // Quiz states
  const [receivedQuizId, setReceivedQuizId] = useState(null);
  const [showQuizNotification, setShowQuizNotification] = useState(false);
  const [isQuizOpen, setIsQuizOpen] = useState(false);

  // Available languages
  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' },
  ];

  // Mute/unmute original audio based on translation state
  useEffect(() => {
    remoteAudioTracks.forEach(({ audioElement }) => {
      if (audioElement) {
        audioElement.muted = isReceivingTranslation;
      }
    });
  }, [isReceivingTranslation, remoteAudioTracks]);

  // Poll for available streams
  useEffect(() => {
    trigger();
    const interval = setInterval(() => trigger(), 30000);
    return () => clearInterval(interval);
  }, [trigger]);

  // Handle chat history updates and extract translation session ID
  useEffect(() => {
    if (chatHistoryData) {
      setChatMessages(chatHistoryData);
      if (!isChatOpen) {
        const newMessages = chatHistoryData.length - chatMessages.length;
        if (newMessages > 0) setUnreadMessages((prev) => prev + newMessages);
      }
      // Check for TRANSLATION_SESSION message
      chatHistoryData.forEach((message) => {
        if (message.message.startsWith('TRANSLATION_SESSION:')) {
          const sessionId = message.message.split(':')[1];
          if (sessionId && sessionId !== translationSessionId) {
            setTranslationSessionId(sessionId);
            setInputSessionId(sessionId);
            toast.success('Received translation session ID via chat');
          }
        }
      });
    }
  }, [chatHistoryData]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (livekitRoom) livekitRoom.disconnect();
      cleanupTranslation();
    };
  }, [livekitRoom]);

  // Audio queue management
  const addToAudioQueue = (audioData) => {
    audioQueueRef.current.push(audioData);
    setAudioQueueLength(audioQueueRef.current.length);
    processAudioQueue();
  };

  const processAudioQueue = async () => {
    if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) return;
    isProcessingQueueRef.current = true;
    while (audioQueueRef.current.length > 0) {
      const audioData = audioQueueRef.current.shift();
      setAudioQueueLength(audioQueueRef.current.length);
      await playAudioFromQueue(audioData);
    }
    isProcessingQueueRef.current = false;
    setAudioQueueLength(0);
  };

  const stopCurrentAudio = () => {
    if (currentAudioSourceRef.current) {
      try {
        currentAudioSourceRef.current.stop();
        currentAudioSourceRef.current.disconnect();
      } catch (error) {
        console.warn('⚠️ Error stopping Web Audio source:', error);
      }
      currentAudioSourceRef.current = null;
    }
    if (currentHtmlAudioRef.current) {
      try {
        currentHtmlAudioRef.current.pause();
        currentHtmlAudioRef.current.currentTime = 0;
        if (currentHtmlAudioRef.current.src) {
          URL.revokeObjectURL(currentHtmlAudioRef.current.src);
        }
      } catch (error) {
        console.warn('⚠️ Error stopping HTML5 Audio:', error);
      }
      currentHtmlAudioRef.current = null;
    }
    setIsPlayingTranslatedAudio(false);
  };

  const clearAudioQueue = () => {
    audioQueueRef.current = [];
    setAudioQueueLength(0);
    stopCurrentAudio();
    isProcessingQueueRef.current = false;
  };

  const playAudioFromQueue = async (audioDataBase64) => {
    return new Promise(async (resolve) => {
      try {
        if (!audioDataBase64) {
          setIsPlayingTranslatedAudio(false);
          resolve();
          return;
        }
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        }
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
        try {
          const binaryString = atob(audioDataBase64);
          const audioData = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            audioData[i] = binaryString.charCodeAt(i);
          }
          const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer);
          const source = audioContextRef.current.createBufferSource();
          source.buffer = audioBuffer;
          source.connect(audioContextRef.current.destination);
          currentAudioSourceRef.current = source;
          setIsPlayingTranslatedAudio(true);
          source.onended = () => {
            setIsPlayingTranslatedAudio(false);
            currentAudioSourceRef.current = null;
            resolve();
          };
          source.start();
        } catch (webAudioError) {
          try {
            const audioBlob = new Blob(
              [
                new Uint8Array(
                  atob(audioDataBase64)
                    .split('')
                    .map((c) => c.charCodeAt(0)),
                ),
              ],
              { type: 'audio/wav' },
            );
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);
            currentHtmlAudioRef.current = audio;
            setIsPlayingTranslatedAudio(true);
            audio.onended = () => {
              setIsPlayingTranslatedAudio(false);
              currentHtmlAudioRef.current = null;
              URL.revokeObjectURL(audioUrl);
              resolve();
            };
            audio.onerror = () => {
              setIsPlayingTranslatedAudio(false);
              currentHtmlAudioRef.current = null;
              URL.revokeObjectURL(audioUrl);
              resolve();
            };
            await audio.play();
          } catch (htmlAudioError) {
            setIsPlayingTranslatedAudio(false);
            resolve();
          }
        }
      } catch (error) {
        setIsPlayingTranslatedAudio(false);
        resolve();
      }
    });
  };

  const cleanupTranslation = () => {
    if (translationEventSource) {
      translationEventSource.close();
      setTranslationEventSource(null);
    }
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    clearAudioQueue();
    setIsReceivingTranslation(false);
    setTranslationSessionId('');
    setInputSessionId('');
    setTranslationStatus('');
    setCurrentTranscription('');
    setCurrentTranslation('');
  };

  const startReceivingTranslation = () => {
    if (!translationSessionId && !inputSessionId) {
      toast.error('Please enter a valid translation session ID');
      return;
    }
    const sessionIdToUse = translationSessionId || inputSessionId;
    const eventSourceUrl = `http://localhost:8022/api/translate/receive-audio/${sessionIdToUse}`;
    const eventSource = new EventSource(eventSourceUrl);

    eventSource.onopen = () => {
      setTranslationStatus('Connected to translation stream');
      setIsReceivingTranslation(true);
      toast.success('Connected to translation stream');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'translated_audio') {
          addToAudioQueue(data.audio_data);
        } else if (data.type === 'heartbeat') {
          setTranslationStatus(`Heartbeat received at ${new Date(data.timestamp * 1000).toLocaleTimeString()}`);
        }
      } catch (error) {
        console.error('Error parsing SSE data:', error);
        setTranslationStatus('Error receiving translation data');
      }
    };

    eventSource.onerror = () => {
      setTranslationStatus('Error in translation stream');
      toast.error('Translation stream disconnected');
      cleanupTranslation();
    };

    setTranslationEventSource(eventSource);
  };

  const stopReceivingTranslation = () => {
    cleanupTranslation();
    toast.success('Stopped receiving translation');
  };

  const handleJoinStream = async (stream) => {
    try {
      const userId = sessionStorage.getItem('userId');
      const response = await joinStream({
        user_id: userId,
        session_id: stream.session_id,
      }).unwrap();
      if (response.token && response.livekit_url) {
        setCurrentStream(stream);
        setIsViewingStream(true);
        setConnectionStatus('Connecting...');
        const room = new Room();
        setLivekitRoom(room);
        await connectToLiveKitRoom(room, response.token, response.livekit_url);
        // Start polling chat history
        getChatHistory(stream.session_id);
      } else {
        toast.error('Invalid join stream response');
      }
    } catch (error) {
      toast.error('Failed to join stream: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const handleLeaveStream = () => {
    if (livekitRoom) {
      livekitRoom.disconnect();
      setLivekitRoom(null);
    }
    setIsViewingStream(false);
    setCurrentStream(null);
    setParticipants([]);
    setRemoteVideoTracks([]);
    setRemoteAudioTracks([]);
    setConnectionStatus('');
    setHasScreenShare(false);
    setHasCameraTrack(false);
    cleanupTranslation();
    toast.success('Left the stream');
  };

  const connectToLiveKitRoom = async (room, token, url) => {
    room.on(RoomEvent.Connected, () => {
      setLivekitConnected(true);
      setConnectionStatus('Connected');
      toast.success('Connected to stream');
    });
    room.on(RoomEvent.Disconnected, () => {
      setLivekitConnected(false);
      setConnectionStatus('Disconnected');
      setIsViewingStream(false);
      toast.info('Disconnected from stream');
    });
    room.on(RoomEvent.ParticipantConnected, (participant) => {
      setParticipants((prev) => [...prev, participant]);
    });
    room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity));
    });
    room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
      if (track.kind === 'video') {
        const videoElement = document.createElement('video');
        videoElement.muted = true;
        videoElement.playsInline = true;
        track.attach(videoElement);
        if (publication.source === Track.Source.ScreenShare) {
          setHasScreenShare(true);
          if (screenVideoRef.current) {
            screenVideoRef.current.innerHTML = '';
            screenVideoRef.current.appendChild(videoElement);
          }
        } else if (publication.source === Track.Source.Camera) {
          setHasCameraTrack(true);
          const targetRef = hasScreenShare ? cameraVideoRef : cameraMainRef;
          if (targetRef.current) {
            targetRef.current.innerHTML = '';
            targetRef.current.appendChild(videoElement);
          }
        }
        setRemoteVideoTracks((prev) => [
          ...prev,
          { track, videoElement, source: publication.source, participant },
        ]);
      } else if (track.kind === 'audio') {
        const audioElement = document.createElement('audio');
        audioElement.autoplay = true;
        audioElement.muted = isReceivingTranslation;
        track.attach(audioElement);
        setRemoteAudioTracks((prev) => [...prev, { track, audioElement, participant }]);
      }
    });
    room.on(RoomEvent.TrackUnsubscribed, (track, publication) => {
      if (track.kind === 'video') {
        setRemoteVideoTracks((prev) =>
          prev.filter((t) => t.track.sid !== track.sid)
        );
        if (publication.source === Track.Source.ScreenShare) {
          setHasScreenShare(false);
        } else if (publication.source === Track.Source.Camera) {
          setHasCameraTrack(false);
        }
      } else if (track.kind === 'audio') {
        setRemoteAudioTracks((prev) =>
          prev.filter((t) => t.track.sid !== track.sid)
        );
      }
    });
    await room.connect(url, token);
  };

  const handleSendChatMessage = async () => {
    if (!newMessage.trim() || !currentStream) return;
    const messageData = {
      session_id: currentStream.session_id,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem('userId'),
      sender_name: sessionStorage.getItem('name') || 'Viewer',
    };
    try {
      await sendChatMessage(messageData).unwrap();
      setNewMessage('');
      setTimeout(() => getChatHistory(currentStream.session_id), 500);
    } catch (error) {
      toast.error('Error sending message: ' + (error.data?.message || 'Unknown error'));
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendChatMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) setUnreadMessages(0);
  };

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'kota_teacher':
      case 'faculty':
        return 'text-emerald-500';
      case 'student':
        return 'text-blue-500';
      case 'center_counselor':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };

  useEffect(() => {
    if (isViewingStream && currentStream) {
      getChatHistory(currentStream.session_id);
      const pollInterval = setInterval(() => getChatHistory(currentStream.session_id), 2000);
      return () => clearInterval(pollInterval);
    }
  }, [isViewingStream, currentStream, getChatHistory]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-gray-900"
    >
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-gray-800 to-indigo-900 text-white p-4 shadow-lg border-b border-gray-700"
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              Live Stream Viewer
            </h1>
            <p className="text-sm text-gray-300">Session: {currentStream?.session_id || 'Not joined'}</p>
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
              <motion.div
                animate={{ scale: connectionStatus === 'Connected' ? [1, 1.2, 1] : 1 }}
                transition={{ repeat: connectionStatus === 'Connected' ? Infinity : 0, duration: 1.5 }}
                className={`w-3 h-3 rounded-full mr-3 ${connectionStatus === 'Connected' ? 'bg-green-400' : 'bg-red-400'}`}
              ></motion.div>
              <span className="text-sm font-medium">{connectionStatus || 'Disconnected'}</span>
            </div>
            <div className="flex items-center bg-gray-800/50 rounded-lg px-4 py-2">
              <svg className="w-4 h-4 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
              <span className="text-sm font-medium">{participants.length} Viewers</span>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="flex h-[calc(100vh-80px)] max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex-1 relative bg-black rounded-lg overflow-hidden shadow-2xl"
        >
          <div className="w-full h-full flex items-center justify-center relative">
            {hasScreenShare ? (
              <div ref={screenVideoRef} className="w-full h-full"></div>
            ) : (
              <div ref={cameraMainRef} className="w-full h-full"></div>
            )}
            {!isViewingStream && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/70 to-black/70"
              >
                <div className="text-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ repeat: Infinity, duration: 2, ease: 'linear' }}
                    className="w-20 h-20 border-4 border-indigo-200 border-t-indigo-500 rounded-full mx-auto mb-4"
                  ></motion.div>
                  <motion.p
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="text-xl font-semibold"
                  >
                    No stream joined
                  </motion.p>
                </div>
              </motion.div>
            )}
          </div>
          {hasCameraTrack && hasScreenShare && (
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.4 }}
              className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-lg overflow-hidden border border-gray-600 shadow-lg"
            >
              <div ref={cameraVideoRef} className="w-full h-full"></div>
              <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                Camera
              </div>
            </motion.div>
          )}
          {!isChatOpen && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleChat}
              className="absolute bottom-6 left-6 bg-indigo-500 hover:bg-indigo-600 text-white p-3 rounded-full shadow-lg"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                />
              </svg>
              {unreadMessages > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                >
                  {unreadMessages}
                </motion.span>
              )}
            </motion.button>
          )}
        </motion.div>

        <AnimatePresence>
          {isChatOpen && (
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="absolute right-0 top-0 bottom-0 w-96 bg-gray-800 text-white shadow-lg border-l border-gray-700 z-10"
            >
              <div className="p-4 border-b border-gray-700 flex justify-between items-center">
                <h3 className="text-lg font-semibold">Live Chat</h3>
                <button onClick={toggleChat} className="text-gray-400 hover:text-white">
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="h-[calc(100%-120px)] overflow-y-auto p-4">
                {isLoadingHistory ? (
                  <div className="flex items-center justify-center h-full">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 1 }}
                      className="w-6 h-6 border-2 border-indigo-200 border-t-indigo-500 rounded-full"
                    ></motion.div>
                  </div>
                ) : chatMessages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    No messages yet
                  </div>
                ) : (
                  <AnimatePresence>
                    {chatMessages.map((message, index) => (
                      <motion.div
                        key={`${message.id || 'msg'}-${index}-${message.timestamp || Date.now()}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="p-2 hover:bg-gray-700/30 rounded mb-2"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className={`text-sm font-medium ${getRoleColor(message.sender_role)}`}>
                                {message.sender_name}
                              </span>
                              <span className="text-xs bg-gray-700 px-2 py-1 rounded">{message.sender_role}</span>
                            </div>
                            <p className="text-sm text-gray-200">{message.message}</p>
                          </div>
                          <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-800 border-t border-gray-700">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Type a message..."
                    className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
                  />
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleSendChatMessage}
                    disabled={!newMessage.trim() || isSendingMessage}
                    className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
                  >
                    {isSendingMessage ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ repeat: Infinity, duration: 1 }}
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                      ></motion.div>
                    ) : (
                      'Send'
                    )}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-20 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
        >
          <div className="flex flex-col items-center py-4 space-y-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('info')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'info' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('participants')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'participants' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('translation')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'translation' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setActiveSidebarTab('quiz')}
              className={`p-3 rounded-lg ${activeSidebarTab === 'quiz' ? 'bg-indigo-600' : 'bg-gray-700 hover:bg-gray-600'}`}
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2m-6 0a2 2 0 002 2h2a2 2 0 002-2m-6 0a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </motion.button>
          </div>
        </motion.div>

        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-80 bg-gray-800 text-white overflow-y-auto shadow-lg border-l border-gray-700"
        >
          <div className="p-6 space-y-6">
            {activeSidebarTab === 'info' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Stream Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                    <span className="text-gray-300">Session ID:</span>
                    <span className="font-medium text-indigo-400">{currentStream?.session_id || 'Not joined'}</span>
                  </div>
                  <div className="flex justify-between bg-gray-700/50 p-2 rounded">
                    <span className="text-gray-300">Status:</span>
                    <span className="font-medium text-indigo-400">{connectionStatus || 'Disconnected'}</span>
                  </div>
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'participants' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Participants ({participants.length})</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <AnimatePresence>
                    {participants.map((participant, index) => (
                      <motion.div
                        key={participant.identity || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="flex items-center bg-indigo-500/10 p-2 rounded"
                      >
                        <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-sm font-bold mr-2">
                          {participant.identity?.charAt(0)?.toUpperCase() || 'U'}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-indigo-400">{participant.identity || 'Unknown'}</div>
                          <div className="text-xs text-gray-400">Viewer</div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'translation' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Translation</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 font-medium">Translation</span>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={isReceivingTranslation ? stopReceivingTranslation : startReceivingTranslation}
                      className={`px-4 py-2 rounded-lg font-semibold ${
                        isReceivingTranslation ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-500 hover:bg-indigo-600'
                      } text-white`}
                    >
                      {isReceivingTranslation ? 'Stop' : 'Receive Translation'}
                    </motion.button>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Session ID</label>
                    <input
                      type="text"
                      value={inputSessionId}
                      onChange={(e) => setInputSessionId(e.target.value)}
                      placeholder="Enter translation session ID"
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white"
                      disabled={isReceivingTranslation}
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Language</label>
                    <select
                      value={targetLanguage}
                      onChange={(e) => setTargetLanguage(e.target.value)}
                      disabled={isReceivingTranslation}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50"
                    >
                      {availableLanguages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                      <motion.div
                        animate={{ scale: isReceivingTranslation ? [1, 1.2, 1] : 1 }}
                        transition={{ repeat: isReceivingTranslation ? Infinity : 0, duration: 1.5 }}
                        className={`w-3 h-3 rounded-full mr-2 ${isReceivingTranslation ? 'bg-green-400' : 'bg-gray-400'}`}
                      ></motion.div>
                      <span className="text-sm">{translationStatus || 'Translation Inactive'}</span>
                    </div>
                    {isPlayingTranslatedAudio && (
                      <div className="flex items-center bg-indigo-500/10 p-2 rounded">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ repeat: Infinity, duration: 1 }}
                          className="w-3 h-3 bg-indigo-400 rounded-full mr-2"
                        ></motion.div>
                        <span className="text-sm">Playing Translated Audio</span>
                      </div>
                    )}
                    {audioQueueLength > 0 && (
                      <div className="flex items-center bg-yellow-500/10 p-2 rounded">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ repeat: Infinity, duration: 1 }}
                          className="w-3 h-3 bg-yellow-400 rounded-full mr-2"
                        ></motion.div>
                        <span className="text-sm">Audio Queue: {audioQueueLength} pending</span>
                      </div>
                    )}
                  </div>
                  {currentTranscription && (
                    <div className="bg-gray-700/50 p-2 rounded text-sm">
                      <span className="font-medium">Transcription:</span> {currentTranscription}
                    </div>
                  )}
                  {currentTranslation && (
                    <div className="bg-gray-700/50 p-2 rounded text-sm">
                      <span className="font-medium">Translation:</span> {currentTranslation}
                    </div>
                  )}
                </div>
              </motion.div>
            )}
            {activeSidebarTab === 'quiz' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="bg-gray-900 rounded-lg p-4 shadow"
              >
                <h3 className="text-lg font-semibold mb-3">Live Quiz</h3>
                {receivedQuizId ? (
                  <div className="space-y-4">
                    <p className="text-sm text-gray-300">A quiz has been assigned!</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setIsQuizOpen(true)}
                      className="w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
                    >
                      Start Quiz
                    </motion.button>
                  </div>
                ) : (
                  <p className="text-sm text-gray-400">No quiz available</p>
                )}
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>

      <AnimatePresence>
        {data?.active_streams?.length > 0 && !isViewingStream && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.5 }}
            className="fixed bottom-6 left-6 right-6 max-w-7xl mx-auto bg-gray-800 rounded-lg shadow-lg p-4 z-20"
          >
            <h3 className="text-lg font-semibold text-white mb-3">Available Streams</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.active_streams.map((stream) => (
                <motion.div
                  key={stream.session_id}
                  whileHover={{ scale: 1.05 }}
                  className="bg-gray-900 p-4 rounded-lg"
                >
                  <h4 className="text-white font-medium">{stream.title}</h4>
                  <p className="text-sm text-gray-400 mb-2">Session: {stream.session_id}</p>
                  <motion.button
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleJoinStream(stream)}
                    disabled={isJoining}
                    className="w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg"
                  >
                    {isJoining ? 'Joining...' : 'Join Stream'}
                  </motion.button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {isQuizOpen && (
        <LiveQuiz quizId={receivedQuizId} onClose={() => setIsQuizOpen(false)} />
      )}

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </motion.div>
  );
};

export default CenterTraineeLiveViewer;
